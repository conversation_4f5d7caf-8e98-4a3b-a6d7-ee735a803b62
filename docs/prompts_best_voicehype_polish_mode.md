> As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON> wa barakatu. Co pilot. How are you doing? So sorry, <PERSON><PERSON>. That's your name, right? So your augment code. Okay, so augment code. Today what I want to do is I want to do some data analytics. Insha <PERSON>. So as you know, this is our product, Voice Hype. And in Voice Hype we. It's a SaaS product that helps you code faster by talking instead of typing and it eliminates, almost eliminates the need to type. So it's currently it's only specifically aimed at developers to speed they work when, when they are vibe coding with LLMs. But inshallah, we plan to generalize it to a greater and a bigger, bigger, bigger, bigger audience. Inshallah. By the grace of <PERSON>, <PERSON><PERSON> wa ta' ala. Inshallah in the future, very soon. Very soon. Inshah. Inshallah. So anyways, here is the, the CSV file that I've shared with you. It contains the entire pricings. So for the LLMs, it's per token and transcription it is per minute. Okay? And for transcription models, we have only like three models from two providers. The first provider is assembly AI. The second provider is Whisper and, sorry, <PERSON>. Okay, And Whisper model is from Lemon Fox. While best in nano models of Assembly AI are from assembly AI. What we want to do, In<PERSON><PERSON><PERSON>, is we want to create like subscription models, subscription pricing because people tend to like subscriptions instead of inserting credits. So our competitors, for example, they have monthly subscription and yearly subscription for like, let's say for example, like different kind of subscription packages. So for example, for basic you can have like for, you know, for people who want to do average or low usage. And then for pro, you have a lot more quota, like lot more hours and a lot more tokens. And for very, very, very, very, very pro, you have a lot, a lot, a lot, a lot of tokens. Anyways, what? So let me just check. No, no, no, not right now, Mariam. Not right now. All right, Ogi, I am back. I'm very sorry. My little, little sister, she came in, in, so I needed to get her out, out. Subhanallah. Okay, so now I'm continuing. As I was saying previously, we want to develop packages, inshallah. But for that I would love to check out competitor pricings. So one of our very, very, very dangerous, dangerous, dangerous competitor is Whisper Floor. Now I want to, I want to go to their pricing page. Where is their optimization of different custom things more. Okay, so here in Whisper Flow for their monthly pricing, they have first of all the flow promote. Okay so this is their name. It is 15 per month. And I want my like subscription packages to start from maybe $9, $9 per month, the lowest package. And then you can decide the next packages. So I want you to do some data analytics. Like for example, like we can calculate the average price of transcription per minute by you know, averaging the prices of all the transcription models. Okay. And then we can like do thumbs, do some statistics. Like we can like for the nine dollar package, like how many, like you can calculate like how many hours we want and how many tokens we want. We want to balance that. Are we going to somehow calculate a calculation which will balance it out? So for example, like how many minutes. It's like a very, like, it's a, it's an optimization problem, mathematical optimal optimization problem that we want to solve. Subhanallah. So it is like this, that you know, how many average number of words do people speak in one minute? And then we want to, you know, because. And then we want to assume that one token is equal to one word. Okay? And we want to also assume that the number of output tokens, the ratio. Let's just assume that the number of output tokens is actually. Okay, so the prompt that we Send to the LLM also has like 500, almost 500 tokens by default, each prompt. So we want to accommodate that as well. So that's a fixed number of tokens. And then the variable, the variable number of tokens is the transcription itself. Okay, so subhanallah we want to calculate like how many hours should be in the nine dollar package and how many, how many number of tokens it should be in that nine dollar package such that the number of tokens are enough for the number of hours it should be balanced. As I said, you know, it's like a weighing scale and you want to calculate the balance. Subhanallah. So the prices already contain our profits. So you don't need to like apply any margin or anything. But yes, please let us know. And I want you to, I want you to do the data analytics in Python. Okay. And you can use pandas. Okay. Jazakallah.

Assalamu alaikum wa rahmatullahi wa barakatuh,

Today I would like to conduct data analytics for our SaaS product, Voice Hype. This product helps developers code more efficiently by converting speech to text, essentially eliminating the need for typing. Currently, it specifically targets developers to accelerate their workflow when coding with Large Language Models (LLMs). We intend to expand our target audience in the near future.

Regarding the CSV file I have shared, it contains comprehensive pricing information. For LLMs, pricing is calculated per token, while transcription services are billed per minute. We utilize three transcription models from two providers: Assembly AI (offering Best and Nano models) and Lemon Fox (providing the Whisper model).

Our objective is to create subscription pricing models, as customers generally prefer subscriptions over credit-based systems. Our competitors implement monthly and yearly subscription packages with tiered options: basic packages for low usage, pro packages with increased quotas for hours and tokens, and premium packages with substantially higher allowances.

I would like to analyze competitor pricing strategies. One significant competitor, Whisper Flow, offers their base package at $15 per month. I propose our entry-level package start at $9 monthly, with subsequent tiers to be determined based on data analysis.

This requires statistical analysis to determine appropriate allocations of transcription minutes and tokens for each tier. We need to balance these resources effectively, considering the average words spoken per minute and assuming one token equals one word. Additionally, we must account for the fixed token cost (approximately 500 tokens) of each prompt sent to the LLM, plus the variable token count from the transcription itself.

The prices in our dataset already incorporate our profit margins, so no additional adjustments are necessary. Please conduct this data analytics using Python with the Pandas library to establish balanced resource allocations for our subscription tiers.

Jazak Allah Khair.

---

> Mashallah. Mashallah. You've done some really, really great work right there. I just have like two questions from you. I'm really interested, first of all in how you actually calculated this balance. I mean, like, I'm really interested in the mathematics that you did behind that. Subhanallah. I really want to know how you did that. Great job right there. Mashallah, by the way. And the second thing is, like, you've mentioned like, that you've calculated that on a 70% profit margin for the basic tier, we should like 324 minutes plus 210,000 tokens. Right. But in this I want to ask you, like, why did you like, select, like, what does 70% profit margin mean? Because the prices that are there in the CSV table already include like an 18%. 18% profit margin. So I want to see like in different profit margins, what the different number of minutes and tokens would be like. You can like, first of all create it on the same original price and then you can maybe use 10% and then what? How it would look on 20 and then 30 and then 40 and then 50 and 60 and 70 up all the way to 100. And if I'm not wrong, if I'm not thinking wrong, if we set like 100% profit margin, wouldn't that mean zero minutes and zero tokens? I don't know. I might be confused, but I need your clarification on this. Jazakallah, please let me know. Answer the questions that I've asked. Jazakallah, bro, very good work, Mashallah.

I would like to commend you on your exemplary work. I have two inquiries regarding your calculations. Firstly, I am particularly interested in understanding the mathematical methodology you employed to calculate this balance. Your approach was impressive, and I would appreciate insight into your computational process.

Secondly, you mentioned calculating 324 minutes plus 210,000 tokens based on a 70% profit margin for the basic tier. I seek clarification on what this 70% profit margin signifies, especially considering that the prices in the CSV table already incorporate an 18% profit margin.

Could you provide a comparative analysis showing how the number of minutes and tokens would vary across different profit margins? Perhaps you could start with the original price, then demonstrate incremental changes at 10%, 20%, 30%, 40%, 50%, 60%, 70%, and up to 100% profit margins. I am also curious whether a 100% profit margin would effectively result in zero minutes and zero tokens, though I may be misunderstanding the concept.

I would appreciate your clarification on these matters. Thank you again for your excellent work.