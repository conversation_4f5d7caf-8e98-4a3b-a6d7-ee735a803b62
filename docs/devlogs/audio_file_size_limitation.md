# Audio File Size Limitation Enhancement - Development Log

## Date: June 14, 2025

## Overview

This development log investigates issues related to file size limitations during audio recording in the Voice Hype VS Code extension. Currently, extended conversations produce WAV files that can exceed the 25MB limit imposed by our system, resulting in transcription failures. The log outlines a research-based approach to identify the root causes and proposes a robust solution to enhance the user experience by enabling seamless handling of large audio recordings.

## Current Issues

1. **Size Limitation Error Behavior**:
   - When recordings exceed 25MB, the system produces errors
   - Error detection is delayed (40-60 seconds) rather than immediate
   - Possible inefficiency if transcription is attempted before failure

2. **Unnecessary Restrictions**:
   - AssemblyAI supports files up to 490MB, making our 25MB limit unnecessarily restrictive
   - Whisper maintains a 25MB limit, requiring a solution for larger files

3. **User Experience Impact**:
   - Users lose entire transcriptions when recordings exceed size limits
   - No warning or feedback until after recording completion and processing attempt
   - Frustrating experience when longer dictations fail entirely

## Research Findings

### API Service Limitations

| Service | Official Size Limit | Current Implementation Limit |
|---------|---------------------|------------------------------|
| AssemblyAI | 490MB | 25MB |
| Whisper | 25MB | 25MB |

### Code Research

After examining the codebase, we've identified several key components related to the file size limitation issue:

#### Server-Side Size Validation

1. **Primary Size Validation Point**:
   - Located in `supabase/functions/_shared/utils.ts` (line ~229)
   - The `validateAudioData()` function enforces a strict 25MB limit:
   ```typescript
   if (audioData.length > 25 * 1024 * 1024) { // 25MB limit
     throw new Error('Audio file too large (max 25MB)');
   }
   ```

2. **Validation Usage**:
   - This validation is called in `supabase/functions/transcribe/index.ts` (line ~764)
   - Occurs after the audio data is received from the client and converted from base64
   - The validation happens at the beginning of the API request handling process

#### Client-Side Processing

1. **TranscriptionService Flow**:
   - The client records audio and saves it to a temporary file
   - File size is logged but not validated on the client side:
   ```typescript
   // From TranscriptionService.ts
   console.log(`VoiceHype: Recording file size: ${stats.size} bytes`);
   ```

2. **API Request Process**:
   - Audio is encoded to base64 and sent to the server
   - No client-side size validation before sending
   - The 40-60 second delay before error likely comes from:
     - Time to encode large audio to base64
     - Network transmission time for large payload
     - Server processing time before size validation

## Proposed Solution

### 1. File Size Detection and Management

**Client-Side Size Detection**:
- Implement real-time file size monitoring during recording in `RecordingService.ts`
- Add a method to estimate WAV file size based on recording duration and sample rate
- Provide visual feedback to users about approaching size limits via the webview UI

**Automatic File Splitting**:
- When a recording exceeds or approaches service-specific limits, automatically split into smaller chunks
- Implementation would occur in a new utility class (e.g., `AudioSplitter.ts`) with methods:
  ```typescript
  // Proposed new utility methods
  function splitAudioFile(filePath: string, maxChunkSizeBytes: number): Promise<string[]>;
  function estimateChunkCount(filePath: string, maxChunkSizeBytes: number): Promise<number>;
  ```
- Calculate optimal chunk boundaries based on file size and target chunk size
- Create a sequence of smaller audio files that maintain audio quality

### 2. Parallel Processing

**Concurrent API Requests**:
- Modify `TranscriptionService.ts` to handle multiple audio chunks:
  ```typescript
  // Proposed new method in TranscriptionService
  async transcribeAudioChunks(
    filePaths: string[], 
    options: TranscriptionOptions
  ): Promise<TranscriptionResult[]>
  ```
- Use Promise.all() to process multiple API requests simultaneously
- Track progress of each chunk's transcription and aggregate results
- Implement chunk-specific error handling to allow partial success

**Results Aggregation**:
- Create a new utility function to merge transcription results:
  ```typescript
  // Proposed utility function
  function mergeTranscriptionResults(
    results: TranscriptionResult[]
  ): TranscriptionResult
  ```
- Concatenate text segments in the correct order
- Add punctuation between segments if needed
- Preserve metadata like timestamps and confidence scores

### 3. Service-Specific Optimizations

**AssemblyAI Implementation**:
- Update the size limit for AssemblyAI in `supabase/functions/_shared/utils.ts`:
  ```typescript
  // Change from
  if (audioData.length > 25 * 1024 * 1024) { // 25MB limit
    throw new Error('Audio file too large (max 25MB)');
  }
  
  // To service-specific limits
  if (service === 'assemblyai' && audioData.length > 100 * 1024 * 1024) {
    throw new Error('Audio file too large for AssemblyAI (max 100MB)');
  } else if (service === 'openai' && audioData.length > 25 * 1024 * 1024) {
    throw new Error('Audio file too large for Whisper (max 25MB)');
  }
  ```
- Implement splitting only for files >100MB for AssemblyAI
- Add configuration option for maximum AssemblyAI chunk size (default 100MB)

**Whisper Implementation**:
- Maintain 25MB limit enforcement for Whisper API
- Add audio compression options before splitting:
  ```typescript
  // Proposed utility function
  async function compressAudioFile(
    filePath: string, 
    targetSizeBytes: number
  ): Promise<string>
  ```
- Configure chunk size for optimal Whisper performance (15-20MB per chunk)

### 4. User Experience Enhancements

**Progress Indicators**:
- Add progress UI components in the webview for multi-chunk processing:
  ```typescript
  // Example message to webview
  this._view?.webview.postMessage({
    command: 'chunkTranscriptionProgress',
    current: 2,
    total: 5,
    estimatedTimeRemaining: 45 // seconds
  });
  ```
- Show real-time updates during parallel processing
- Display chunk completion status and combined progress

**Configuration Options**:
- Add user preferences in VS Code settings:
  ```json
  "voicehype.transcription.autoSplitLargeFiles": true,
  "voicehype.transcription.maxChunkSizeAssemblyAI": 100,
  "voicehype.transcription.maxChunkSizeWhisper": 20,
  "voicehype.transcription.compressBeforeSplitting": true
  ```
- Implement settings UI in the webview for these options
- Allow users to choose between quality and file size optimization

## Technical Implementation Plan

### Phase 1: Client-Side File Size Monitoring

1. **Enhance RecordingService**:
   - Add file size monitoring in `RecordingService.ts`
   - Implement method to calculate current/expected WAV file size during recording:
   ```typescript
   private calculateExpectedFileSize(durationMs: number): number {
     const bytesPerSecond = this.sampleRate * 2 * (this.numChannels || 1);
     return (durationMs / 1000) * bytesPerSecond + 44; // 44-byte WAV header
   }
   ```
   - Add warning threshold triggers when approaching limits

2. **UI Indicators**:
   - Create new progress component with size indicators
   - Send file size status to webview regularly:
   ```typescript
   this._view?.webview.postMessage({
     command: 'recordingSizeUpdate',
     currentSizeBytes: currentSize,
     maxSizeBytes: maxSize,
     percentOfLimit: (currentSize / maxSize) * 100
   });
   ```
   - Display warnings when approaching size thresholds

### Phase 2: Audio Splitting Implementation

1. **Create AudioSplitter Utility**:
   - Implement in `src/utils/audioSplitter.ts`
   - Core splitting function to divide WAV files with clean boundaries
   - Support for both time-based and size-based splitting
   ```typescript
   export async function splitAudioFile(
     inputPath: string, 
     outputDir: string, 
     options: {
       maxChunkSizeBytes?: number;
       maxChunkDurationMs?: number;
       detectSilence?: boolean;
     }
   ): Promise<string[]>
   ```

2. **WAV File Manipulation Utilities**:
   - Add functions to read/write WAV headers
   - Implement precise chunk extraction with proper headers
   - Ensure audio quality is maintained in split files

3. **Compression Options**:
   - Implement audio quality/bitrate reduction for Whisper
   - Support sample rate conversion to reduce file size
   - Add silence removal option to reduce file size

### Phase 3: Transcription Service Enhancements

1. **Update TranscriptionService**:
   - Modify `transcribeAudio` method to support chunked files
   - Implement parallel processing:
   ```typescript
   const chunkPromises = audioChunks.map(chunk => 
     this.transcribeAudioChunk(chunk, options)
   );
   const results = await Promise.all(chunkPromises);
   ```
   - Add result aggregation logic

2. **Enhance Error Handling**:
   - Implement chunk-specific retry logic
   - Support partial success with degraded results
   - Add detailed error reporting for failed chunks

3. **Server-Side Updates**:
   - Modify `supabase/functions/_shared/utils.ts` to use service-specific limits
   - Update error messages to be more descriptive about size limits
   - Add detailed logging for size-related failures

### Phase 4: User Configuration & UI

1. **Add Extension Settings**:
   - Update `package.json` with new configuration options:
   ```json
   "voicehype.transcription.autoSplitLargeFiles": {
     "type": "boolean",
     "default": true,
     "description": "Automatically split large audio files for transcription"
   },
   "voicehype.transcription.maxChunkSizeAssemblyAI": {
     "type": "number",
     "default": 100,
     "description": "Maximum chunk size in MB for AssemblyAI transcriptions"
   }
   ```
   - Implement settings handlers in configuration service

2. **Progress UI Components**:
   - Create multi-chunk progress display component
   - Add cancellation support for in-progress operations
   - Implement real-time status updates during processing

3. **User Guidance**:
   - Add tooltips and documentation about file size limits
   - Create user-friendly error messages
   - Show optimization recommendations for large recordings

## Testing Strategy

1. **Size Limit Testing**:
   - Generate test audio files of various sizes (10MB, 25MB, 50MB, 100MB)
   - Verify proper splitting behavior at different thresholds
   - Test with both AssemblyAI and Whisper services

2. **Performance Testing**:
   - Measure transcription time with and without splitting
   - Evaluate parallel processing efficiency
   - Benchmark memory usage during large file processing

3. **Error Handling**:
   - Simulate network failures during multi-chunk processing
   - Test recovery from partial failures
   - Validate result aggregation with missing chunks

4. **User Experience**:
   - Test progress indicators with long recordings
   - Verify cancellation functionality
   - Evaluate overall user flow with large recordings

## Next Steps

1. **Immediate Actions**:
   - Add client-side size estimation during recording
   - Implement basic UI warnings for approaching size limits
   - Add file inspection utility to analyze WAV file structures

2. **Core Feature Development**:
   - Develop the AudioSplitter utility as a standalone component
   - Modify TranscriptionService to support chunked processing
   - Update server-side validation with service-specific limits

3. **UX Implementation**:
   - Design and implement progress UI for chunked transcription
   - Add configuration options to settings panel
   - Create user documentation for the new features

---

This enhanced development log provides a comprehensive plan to address the file size limitation issues in the Voice Hype extension. The implementation will significantly improve user experience by enabling seamless handling of large audio recordings without unnecessary restrictions, InshaaAllah!