<!DOCTYPE html><html class="default" lang="en"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>RtAudioErrorType | Audify.js</title><meta name="description" content="Documentation for Audify.js"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">Audify.js</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../modules.html">Audify.js</a></li><li><a href="RtAudioErrorType.html">RtAudioErrorType</a></li></ul><h1>Enumeration RtAudioErrorType<code class="tsd-tag ts-flagConst">Const</code> </h1></div><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><p>RtAudio error types</p>
</div><div class="tsd-comment tsd-typography"></div></section><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L91">index.d.ts:91</a></li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-index-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg> Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Enumeration Members</h3><div class="tsd-index-list"><a href="RtAudioErrorType.html#DEBUG_WARNING" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>DEBUG_<wbr/>WARNING</span></a>
<a href="RtAudioErrorType.html#DRIVER_ERROR" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>DRIVER_<wbr/>ERROR</span></a>
<a href="RtAudioErrorType.html#INVALID_DEVICE" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>INVALID_<wbr/>DEVICE</span></a>
<a href="RtAudioErrorType.html#INVALID_PARAMETER" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>INVALID_<wbr/>PARAMETER</span></a>
<a href="RtAudioErrorType.html#INVALID_USE" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>INVALID_<wbr/>USE</span></a>
<a href="RtAudioErrorType.html#MEMORY_ERROR" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>MEMORY_<wbr/>ERROR</span></a>
<a href="RtAudioErrorType.html#NO_DEVICES_FOUND" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>NO_<wbr/>DEVICES_<wbr/>FOUND</span></a>
<a href="RtAudioErrorType.html#SYSTEM_ERROR" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>SYSTEM_<wbr/>ERROR</span></a>
<a href="RtAudioErrorType.html#THREAD_ERROR" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>THREAD_<wbr/>ERROR</span></a>
<a href="RtAudioErrorType.html#UNSPECIFIED" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>UNSPECIFIED</span></a>
<a href="RtAudioErrorType.html#WARNING" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>WARNING</span></a>
</div></section></div></details></section></section><section class="tsd-panel-group tsd-member-group"><h2>Enumeration Members</h2><section class="tsd-panel tsd-member"><a id="DEBUG_WARNING" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>DEBUG_<wbr/>WARNING</span><a href="#DEBUG_WARNING" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">DEBUG_<wbr/>WARNING</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">1</span></div><div class="tsd-comment tsd-typography"><p>A non-critical error which might be useful for debugging.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L96">index.d.ts:96</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="DRIVER_ERROR" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>DRIVER_<wbr/>ERROR</span><a href="#DRIVER_ERROR" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">DRIVER_<wbr/>ERROR</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">8</span></div><div class="tsd-comment tsd-typography"><p>A system driver error occurred.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L117">index.d.ts:117</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="INVALID_DEVICE" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>INVALID_<wbr/>DEVICE</span><a href="#INVALID_DEVICE" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">INVALID_<wbr/>DEVICE</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">4</span></div><div class="tsd-comment tsd-typography"><p>An invalid device ID was specified.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L105">index.d.ts:105</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="INVALID_PARAMETER" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>INVALID_<wbr/>PARAMETER</span><a href="#INVALID_PARAMETER" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">INVALID_<wbr/>PARAMETER</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">6</span></div><div class="tsd-comment tsd-typography"><p>An invalid parameter was specified to a function.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L111">index.d.ts:111</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="INVALID_USE" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>INVALID_<wbr/>USE</span><a href="#INVALID_USE" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">INVALID_<wbr/>USE</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">7</span></div><div class="tsd-comment tsd-typography"><p>The function was called incorrectly.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L114">index.d.ts:114</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="MEMORY_ERROR" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>MEMORY_<wbr/>ERROR</span><a href="#MEMORY_ERROR" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">MEMORY_<wbr/>ERROR</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">5</span></div><div class="tsd-comment tsd-typography"><p>An error occurred during memory allocation.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L108">index.d.ts:108</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="NO_DEVICES_FOUND" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>NO_<wbr/>DEVICES_<wbr/>FOUND</span><a href="#NO_DEVICES_FOUND" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">NO_<wbr/>DEVICES_<wbr/>FOUND</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">3</span></div><div class="tsd-comment tsd-typography"><p>No devices found on system.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L102">index.d.ts:102</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="SYSTEM_ERROR" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>SYSTEM_<wbr/>ERROR</span><a href="#SYSTEM_ERROR" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">SYSTEM_<wbr/>ERROR</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">9</span></div><div class="tsd-comment tsd-typography"><p>A system error occurred.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L120">index.d.ts:120</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="THREAD_ERROR" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>THREAD_<wbr/>ERROR</span><a href="#THREAD_ERROR" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">THREAD_<wbr/>ERROR</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">10</span></div><div class="tsd-comment tsd-typography"><p>A thread error occurred.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L123">index.d.ts:123</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="UNSPECIFIED" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>UNSPECIFIED</span><a href="#UNSPECIFIED" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">UNSPECIFIED</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">2</span></div><div class="tsd-comment tsd-typography"><p>The default, unspecified error type.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L99">index.d.ts:99</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="WARNING" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>WARNING</span><a href="#WARNING" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">WARNING</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">0</span></div><div class="tsd-comment tsd-typography"><p>A non-critical error.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L93">index.d.ts:93</a></li></ul></aside></section></section></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-index-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-filter-visibility"><h4 class="uppercase">Member Visibility</h4><form><ul id="tsd-filter-options"><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-protected" name="protected"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Protected</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-private" name="private"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Private</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-inherited" name="inherited" checked/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Inherited</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-external" name="external"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>External</span></label></li></ul></form></div><div class="tsd-theme-toggle"><h4 class="uppercase">Theme</h4><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-index-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><a href="#DEBUG_WARNING" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>DEBUG_<wbr/>WARNING</span></a><a href="#DRIVER_ERROR" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>DRIVER_<wbr/>ERROR</span></a><a href="#INVALID_DEVICE" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>INVALID_<wbr/>DEVICE</span></a><a href="#INVALID_PARAMETER" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>INVALID_<wbr/>PARAMETER</span></a><a href="#INVALID_USE" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>INVALID_<wbr/>USE</span></a><a href="#MEMORY_ERROR" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>MEMORY_<wbr/>ERROR</span></a><a href="#NO_DEVICES_FOUND" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>NO_<wbr/>DEVICES_<wbr/>FOUND</span></a><a href="#SYSTEM_ERROR" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>SYSTEM_<wbr/>ERROR</span></a><a href="#THREAD_ERROR" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>THREAD_<wbr/>ERROR</span></a><a href="#UNSPECIFIED" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>UNSPECIFIED</span></a><a href="#WARNING" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>WARNING</span></a></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1"></use></svg><span>Audify.js</span></a><ul class="tsd-small-nested-navigation" id="tsd-nav-container" data-base=".."><li>Loading...</li></ul></nav></div></div></div><footer></footer><div class="overlay"></div></body></html>