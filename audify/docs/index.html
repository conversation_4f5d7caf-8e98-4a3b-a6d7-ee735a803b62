<!DOCTYPE html><html class="default" lang="en"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>Audify.js</title><meta name="description" content="Documentation for Audify.js"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="assets/style.css"/><link rel="stylesheet" href="assets/highlight.css"/><script defer src="assets/main.js"></script><script async src="assets/icons.js" id="tsd-icons-script"></script><script async src="assets/search.js" id="tsd-search-script"></script><script async src="assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search" data-base="."><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="index.html" class="title">Audify.js</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><h2>Audify.js</h2></div><div class="tsd-panel tsd-typography"><p><a href="https://www.npmjs.com/package/audify"><img src="https://badge.fury.io/js/audify.svg" alt="npm version"></a>
<a href="https://github.com/almoghamdani/audify/actions/workflows/commit-build.yml"><img src="https://github.com/almoghamdani/audify/actions/workflows/commit-build.yml/badge.svg?branch=master" alt="Master Build Status"></a>
<a href="https://github.com/almoghamdani/audify/actions/workflows/deploy-build.yml"><img src="https://github.com/almoghamdani/audify/actions/workflows/deploy-build.yml/badge.svg" alt="Prebuilt Build Status"></a></p>
<a id="md:audifyjs" class="tsd-anchor"></a><h1><a href="#md:audifyjs">Audify.js</a></h1><p>Audify.js - Play/Stream/Record PCM audio data &amp; Encode/Decode Opus to PCM audio data</p>
<a id="md:features" class="tsd-anchor"></a><h2><a href="#md:features">Features</a></h2><ul>
<li>Encode 16-bit integer PCM or floating point PCM to Opus packet using C++ Opus library.</li>
<li>Decode Opus packets to 16-bit integer PCM or floating point PCM using C++ Opus library.</li>
<li>Complete API for realtime audio input/output across Linux (native ALSA, JACK, PulseAudio and OSS), Macintosh OS X (CoreAudio and JACK), and Windows (DirectSound, ASIO and WASAPI) operating systems using C++ RtAudio library.</li>
</ul>
<a id="md:installation" class="tsd-anchor"></a><h2><a href="#md:installation">Installation</a></h2><pre><code><span class="hl-0">npm</span><span class="hl-1"> </span><span class="hl-0">install</span><span class="hl-1"> </span><span class="hl-0">audify</span>
</code><button>Copy</button></pre>
<p><strong><em>Most regular installs will support prebuilds that are built with each release.</em></strong></p>
<p><strong><em>Prebuilds are available for Node/Electron versions that support N-API 5-9.</em></strong></p>
<a id="md:requirements-for-source-build" class="tsd-anchor"></a><h4><a href="#md:requirements-for-source-build">Requirements for source build</a></h4><ul>
<li>Node or Electron versions that support N-API 5 and up (<a href="https://nodejs.org/docs/latest/api/n-api.html#node-api-version-matrix">N-API Node Version Matrix</a>)</li>
<li><a href="http://www.cmake.org/download/">CMake</a></li>
<li>A proper C/C++ compiler toolchain of the given platform<ul>
<li><strong>Windows</strong>:<ul>
<li><a href="https://visualstudio.microsoft.com/visual-cpp-build-tools/">Visual C++ Build Tools</a> or a recent version of Visual C++ will do (<a href="https://www.visualstudio.com/products/visual-studio-community-vs">the free Community</a> version works well)</li>
</ul>
</li>
<li><strong>Unix/Posix</strong>:<ul>
<li>Clang or GCC</li>
<li>Ninja or Make (Ninja will be picked if both present)</li>
</ul>
</li>
</ul>
</li>
</ul>
<a id="md:example" class="tsd-anchor"></a><h2><a href="#md:example">Example</a></h2><a id="md:opus-encode-amp-decode" class="tsd-anchor"></a><h4><a href="#md:opus-encode-amp-decode">Opus Encode &amp; Decode</a></h4><pre><code class="language-javascript"><span class="hl-2">const</span><span class="hl-1"> { </span><span class="hl-3">OpusEncoder</span><span class="hl-1">, </span><span class="hl-3">OpusDecoder</span><span class="hl-1">, </span><span class="hl-3">OpusApplication</span><span class="hl-1"> } = </span><span class="hl-4">require</span><span class="hl-1">(</span><span class="hl-5">&quot;audify&quot;</span><span class="hl-1">);</span><br/><br/><span class="hl-6">// Init encoder and decoder</span><br/><span class="hl-6">// Sample rate is 48kHz and the amount of channels is 2</span><br/><span class="hl-6">// The opus coding mode is optimized for audio</span><br/><span class="hl-2">const</span><span class="hl-1"> </span><span class="hl-3">encoder</span><span class="hl-1"> = </span><span class="hl-2">new</span><span class="hl-1"> </span><span class="hl-4">OpusEncoder</span><span class="hl-1">(</span><span class="hl-7">48000</span><span class="hl-1">, </span><span class="hl-7">2</span><span class="hl-1">, </span><span class="hl-0">OpusApplication</span><span class="hl-1">.</span><span class="hl-3">OPUS_APPLICATION_AUDIO</span><span class="hl-1">);</span><br/><span class="hl-2">const</span><span class="hl-1"> </span><span class="hl-3">decoder</span><span class="hl-1"> = </span><span class="hl-2">new</span><span class="hl-1"> </span><span class="hl-4">OpusDecoder</span><span class="hl-1">(</span><span class="hl-7">48000</span><span class="hl-1">, </span><span class="hl-7">2</span><span class="hl-1">);</span><br/><br/><span class="hl-2">const</span><span class="hl-1"> </span><span class="hl-3">frameSize</span><span class="hl-1"> = </span><span class="hl-7">1920</span><span class="hl-1">; </span><span class="hl-6">// 40ms</span><br/><span class="hl-2">const</span><span class="hl-1"> </span><span class="hl-3">buffer</span><span class="hl-1"> = ...</span><br/><br/><span class="hl-6">// Encode and then decode</span><br/><span class="hl-2">var</span><span class="hl-1"> </span><span class="hl-0">encoded</span><span class="hl-1"> = </span><span class="hl-0">encoder</span><span class="hl-1">.</span><span class="hl-4">encode</span><span class="hl-1">(</span><span class="hl-0">buffer</span><span class="hl-1">, </span><span class="hl-0">frameSize</span><span class="hl-1">);</span><br/><span class="hl-2">var</span><span class="hl-1"> </span><span class="hl-0">decoded</span><span class="hl-1"> = </span><span class="hl-0">decoder</span><span class="hl-1">.</span><span class="hl-4">decode</span><span class="hl-1">(</span><span class="hl-0">encoded</span><span class="hl-1">, </span><span class="hl-0">frameSize</span><span class="hl-1">);</span>
</code><button>Copy</button></pre>
<a id="md:record-audio-and-play-it-back-realtime" class="tsd-anchor"></a><h4><a href="#md:record-audio-and-play-it-back-realtime">Record audio and play it back realtime</a></h4><pre><code class="language-javascript"><span class="hl-2">const</span><span class="hl-1"> { </span><span class="hl-3">RtAudio</span><span class="hl-1">, </span><span class="hl-3">RtAudioFormat</span><span class="hl-1"> } = </span><span class="hl-4">require</span><span class="hl-1">(</span><span class="hl-5">&quot;audify&quot;</span><span class="hl-1">);</span><br/><br/><span class="hl-6">// Init RtAudio instance using default sound API</span><br/><span class="hl-2">const</span><span class="hl-1"> </span><span class="hl-3">rtAudio</span><span class="hl-1"> = </span><span class="hl-2">new</span><span class="hl-1"> </span><span class="hl-4">RtAudio</span><span class="hl-1">(</span><span class="hl-6">/* Insert here specific API if needed */</span><span class="hl-1">);</span><br/><br/><span class="hl-6">// Open the input/output stream</span><br/><span class="hl-0">rtAudio</span><span class="hl-1">.</span><span class="hl-4">openStream</span><span class="hl-1">(</span><br/><span class="hl-1">  {</span><br/><span class="hl-1">    </span><span class="hl-0">deviceId:</span><span class="hl-1"> </span><span class="hl-0">rtAudio</span><span class="hl-1">.</span><span class="hl-4">getDefaultOutputDevice</span><span class="hl-1">(), </span><span class="hl-6">// Output device id (Get all devices using `getDevices`)</span><br/><span class="hl-1">    </span><span class="hl-0">nChannels:</span><span class="hl-1"> </span><span class="hl-7">1</span><span class="hl-1">, </span><span class="hl-6">// Number of channels</span><br/><span class="hl-1">    </span><span class="hl-0">firstChannel:</span><span class="hl-1"> </span><span class="hl-7">0</span><span class="hl-1">, </span><span class="hl-6">// First channel index on device (default = 0).</span><br/><span class="hl-1">  },</span><br/><span class="hl-1">  {</span><br/><span class="hl-1">    </span><span class="hl-0">deviceId:</span><span class="hl-1"> </span><span class="hl-0">rtAudio</span><span class="hl-1">.</span><span class="hl-4">getDefaultInputDevice</span><span class="hl-1">(), </span><span class="hl-6">// Input device id (Get all devices using `getDevices`)</span><br/><span class="hl-1">    </span><span class="hl-0">nChannels:</span><span class="hl-1"> </span><span class="hl-7">1</span><span class="hl-1">, </span><span class="hl-6">// Number of channels</span><br/><span class="hl-1">    </span><span class="hl-0">firstChannel:</span><span class="hl-1"> </span><span class="hl-7">0</span><span class="hl-1">, </span><span class="hl-6">// First channel index on device (default = 0).</span><br/><span class="hl-1">  },</span><br/><span class="hl-1">  </span><span class="hl-0">RtAudioFormat</span><span class="hl-1">.</span><span class="hl-3">RTAUDIO_SINT16</span><span class="hl-1">, </span><span class="hl-6">// PCM Format - Signed 16-bit integer</span><br/><span class="hl-1">  </span><span class="hl-7">48000</span><span class="hl-1">, </span><span class="hl-6">// Sampling rate is 48kHz</span><br/><span class="hl-1">  </span><span class="hl-7">1920</span><span class="hl-1">, </span><span class="hl-6">// Frame size is 1920 (40ms)</span><br/><span class="hl-1">  </span><span class="hl-5">&quot;MyStream&quot;</span><span class="hl-1">, </span><span class="hl-6">// The name of the stream (used for JACK Api)</span><br/><span class="hl-1">  (</span><span class="hl-0">pcm</span><span class="hl-1">) </span><span class="hl-2">=&gt;</span><span class="hl-1"> </span><span class="hl-0">rtAudio</span><span class="hl-1">.</span><span class="hl-4">write</span><span class="hl-1">(</span><span class="hl-0">pcm</span><span class="hl-1">) </span><span class="hl-6">// Input callback function, write every input pcm data to the output buffer</span><br/><span class="hl-1">);</span><br/><br/><span class="hl-6">// Start the stream</span><br/><span class="hl-0">rtAudio</span><span class="hl-1">.</span><span class="hl-4">start</span><span class="hl-1">();</span>
</code><button>Copy</button></pre>
<a id="md:documentation" class="tsd-anchor"></a><h2><a href="#md:documentation">Documentation</a></h2><p>Full documentation available <a href="https://almoghamdani.github.io/audify/">here</a>.</p>
<a id="md:legal" class="tsd-anchor"></a><h2><a href="#md:legal">Legal</a></h2><p>This project is licensed under the MIT license.</p>
</div></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-index-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-filter-visibility"><h4 class="uppercase">Member Visibility</h4><form><ul id="tsd-filter-options"><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-protected" name="protected"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Protected</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-private" name="private"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Private</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-inherited" name="inherited" checked/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Inherited</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-external" name="external"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>External</span></label></li></ul></form></div><div class="tsd-theme-toggle"><h4 class="uppercase">Theme</h4><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-index-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><a href="#md:audifyjs"><span>Audify.js</span></a><ul><li><a href="#md:features"><span>Features</span></a></li><li><a href="#md:installation"><span>Installation</span></a></li><li><ul><li><ul><li><a href="#md:requirements-for-source-build"><span>Requirements for source build</span></a></li></ul></li></ul></li><li><a href="#md:example"><span>Example</span></a></li><li><ul><li><ul><li><a href="#md:opus-encode-amp-decode"><span>Opus <wbr/>Encode &amp; <wbr/>Decode</span></a></li><li><a href="#md:record-audio-and-play-it-back-realtime"><span>Record audio and play it back realtime</span></a></li></ul></li></ul></li><li><a href="#md:documentation"><span>Documentation</span></a></li><li><a href="#md:legal"><span>Legal</span></a></li></ul></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="modules.html" class="current"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="assets/icons.svg#icon-1"></use></svg><span>Audify.js</span></a><ul class="tsd-small-nested-navigation" id="tsd-nav-container" data-base="."><li>Loading...</li></ul></nav></div></div></div><footer></footer><div class="overlay"></div></body></html>