{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/node_modules/node-addon-api", "${workspaceFolder}/vendor/opus/include", "${workspaceFolder}/vendor/rtaudio", "C:/Users/<USER>/.node-gyp/**"], "cStandard": "c11", "cppStandard": "c++17"}, {"name": "<PERSON>", "includePath": ["${workspaceFolder}/node_modules/node-addon-api", "${workspaceFolder}/vendor/opus/include", "${workspaceFolder}/vendor/rtaudio", "/usr/local/include/node", "${env:HOME}/.node-gyp/**"], "cStandard": "c11", "cppStandard": "c++17"}, {"name": "Linux", "includePath": ["${workspaceFolder}/node_modules/node-addon-api", "${workspaceFolder}/vendor/opus/include", "${workspaceFolder}/vendor/rtaudio", "/usr/include/node", "${env:HOME}/.node-gyp/**"], "cStandard": "c11", "cppStandard": "c++17"}], "version": 4}