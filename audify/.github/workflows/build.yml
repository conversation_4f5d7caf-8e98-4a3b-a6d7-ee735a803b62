name: Build

on:
  workflow_call:
    inputs:
      deploy:
        description: Whether to build for deployment + Deploy prebuilt binaries
        required: false
        type: boolean
        default: false

jobs:
  build:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [windows-latest, ubuntu-latest, macos-latest]
        arch: [x64, ia32, arm64, arm]
        include:
          - os: ubuntu-latest
            arch: x64
            apt-arch: amd64
          - os: ubuntu-latest
            arch: arm64
            apt-arch: arm64
            toolchain-arch: aarch64
          - os: ubuntu-latest
            arch: arm
            apt-arch: armhf
            toolchain-arch: arm
            gnu-name: gnueabihf
        exclude:
          - os: windows-latest
            arch: arm64
          - os: windows-latest
            arch: arm
          - os: ubuntu-latest
            arch: ia32
          - os: macos-latest
            arch: ia32
          - os: macos-latest
            arch: arm

    name: ${{ matrix.os }}-${{ matrix.arch }}

    env:
      CC: "${{ matrix.toolchain-arch && format('{0}-linux-{1}-gcc', matrix.toolchain-arch, matrix.gnu-name || 'gnu') || '' }}"
      CXX: "${{ matrix.toolchain-arch && format('{0}-linux-{1}-g++', matrix.toolchain-arch, matrix.gnu-name || 'gnu') || '' }}"

    steps:
      - name: Check-Out Code
        uses: actions/checkout@v3
        with:
          submodules: true

      - name: Setup CMake/Ninja
        uses: lukka/get-cmake@latest

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: lts/*

      - name: Install Cross-Build Toolchain
        uses: awalsh128/cache-apt-pkgs-action@latest
        if: ${{ runner.os == 'Linux' && matrix.arch != 'x64' }}
        with:
          packages: crossbuild-essential-${{ matrix.apt-arch }}
          version: 1.0

      - name: Prepare Machine For Cross-Build
        if: ${{ runner.os == 'Linux' && matrix.arch != 'x64' }}
        run: |
          sudo dpkg --add-architecture ${{ matrix.apt-arch }}
          sudo sed -i 's#mirror+file#[arch=${{ matrix.apt-arch }}] mirror+file#g' /etc/apt/sources.list
          sudo sed -i 's#http://azure.archive.ubuntu.com/ubuntu#http://ports.ubuntu.com/ubuntu-ports#g' /etc/apt/apt-mirrors.txt
          sudo apt-get update -yq

      - name: Install External Dependencies
        if: runner.os == 'Linux'
        run: sudo apt-get install libasound2:${{ matrix.apt-arch }} libasound2-dev:${{ matrix.apt-arch }} libpulse-dev:${{ matrix.apt-arch }}

      - name: Install Audify Dependencies
        run: npm ci --ignore-scripts

      - name: Build Audify
        if: ${{ !inputs.deploy }}
        run: npm run rebuild -- --arch ${{ matrix.arch }} --cc "${{ env.CC }}" --cxx "${{ env.CXX }}"

      - name: Upload Audify Binaries
        if: ${{ !inputs.deploy }}
        uses: actions/upload-artifact@v3
        with:
          name: audify-${{ github.sha }}-release-${{ matrix.os }}-${{ matrix.arch }}
          path: build/Release/

      - name: Build + Deploy Audify Prebuilt Binaries
        if: ${{ inputs.deploy }}
        run: npm run build-binaries -- -- --arch ${{ matrix.arch }} -- --cc "${{ env.CC }}" --cxx "${{ env.CXX }}"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
