// Requesty optimization function
import { TokenUsage, createTokenUsage } from '../_shared/utils.ts';

// Declare Deno types
declare const Deno: {
  env: {
    get(key: string): string | undefined;
  };
};

// Requesty model mapping
export const REQUESTY_MODELS = {
  // Llama Models
  'llama-3-70b': 'novita/meta-llama/llama-3.1-70b-instruct',
  'llama-3-8b': 'novita/meta-llama/llama-3.1-8b-instruct-max',

  // Claude Models
  'claude-3.5-sonnet': 'anthropic/claude-3-5-sonnet-latest',
  'claude-3.7-sonnet': 'anthropic/claude-3-7-sonnet-latest',
  'claude-4': 'anthropic/claude-sonnet-4-20250514',
  'claude-3.5-haiku': 'anthropic/claude-3-haiku-20240307',

  // DeepSeek Models
  'deepseek-v3': 'deepinfra/deepseek-ai/DeepSeek-V3',
};

// Type for Requesty models
export type RequestyModel = keyof typeof REQUESTY_MODELS;

// Function to get headers as an object
function getHeadersAsObject(headers: Headers): Record<string, string> {
  const obj: Record<string, string> = {};
  headers.forEach((value, key) => {
    obj[key] = value;
  });
  return obj;
}

// Function for Requesty optimization
export async function optimizeWithRequesty(
  text: string,
  model: RequestyModel,
  customPrompt: string,
  maxOutputTokens?: number,
  apiKey?: string,
  clientMessages?: Array<{role: string, content: string}>
): Promise<{ optimizedText: string, tokenUsage: TokenUsage, modelUsed?: string }> {
  // Get API key from environment if not provided
  const REQUESTY_API_KEY = apiKey || Deno.env.get('REQUESTY_API_KEY') || '';

  if (!REQUESTY_API_KEY) {
    throw new Error('Missing Requesty API key');
  }

  console.log('Optimizing with Requesty:', {
    model,
    requestyModel: REQUESTY_MODELS[model],
    textLength: text.length,
    hasCustomPrompt: !!customPrompt,
    customPromptLength: customPrompt?.length || 0,
    hasClientMessages: Array.isArray(clientMessages),
    clientMessagesCount: clientMessages?.length || 0,
    maxOutputTokens
  });

  // Ensure maxOutputTokens is a valid integer
  const validatedMaxTokens = maxOutputTokens && !isNaN(maxOutputTokens) ?
    Math.floor(maxOutputTokens) : 4096;

  // Use client-provided messages if available, otherwise create a basic structure
  let requestBody: any;

  if (Array.isArray(clientMessages) && clientMessages.length > 0) {
    // Use the client's message structure directly
    requestBody = {
      model: REQUESTY_MODELS[model],
      messages: clientMessages,
      max_tokens: validatedMaxTokens,
      temperature: 1.0
    };
    console.log('Using client-provided messages array');
  } else {
    // Create simple message structure with customPrompt as system and text as user message
    requestBody = {
      model: REQUESTY_MODELS[model],
      messages: [
        {
          role: 'system',
          content: customPrompt
        },
        {
          role: 'user',
          content: text
        }
      ],
      max_tokens: validatedMaxTokens,
      temperature: 1.0
    };
    console.log('Using basic message structure');
  }

  console.log('Requesty request structure:', {
    endpoint: 'https://router.requesty.ai/v1/chat/completions',
    modelUsed: REQUESTY_MODELS[model],
    messageCount: requestBody.messages.length,
    maxTokens: validatedMaxTokens
  });

  // Prepare the API request
  const response = await fetch('https://router.requesty.ai/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${REQUESTY_API_KEY}`,
      'X-Source': 'voicehype-edge',
      'User-Agent': 'VoiceHype/1.0'
    },
    body: JSON.stringify(requestBody)
  });

  const responseHeaders = getHeadersAsObject(response.headers);
  console.log('Requesty response headers:', responseHeaders);

  if (!response.ok) {
    const errorText = await response.text();
    console.error('Requesty API error:', {
      status: response.status,
      statusText: response.statusText,
      body: errorText
    });
    throw new Error(`Requesty API error: ${response.status} - ${response.statusText}. Response: ${errorText}`);
  }

  const data = await response.json();
  console.log('Requesty response:', {
    usage: data.usage,
    modelUsed: data.model,
    finishReason: data.choices[0].finish_reason,
    contentLength: data.choices[0].message.content.length
  });

  // Create standardized token usage
  const tokenUsage = createTokenUsage(
    data.usage.prompt_tokens,
    data.usage.completion_tokens,
    {
      input_details: { prompt_tokens: data.usage.prompt_tokens },
      output_details: { completion_tokens: data.usage.completion_tokens }
    }
  );

  return {
    optimizedText: data.choices[0].message.content.trim(),
    tokenUsage,
    modelUsed: data.model // Return the actual model used
  };
}
