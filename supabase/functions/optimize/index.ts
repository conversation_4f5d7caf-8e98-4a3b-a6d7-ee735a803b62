// Supabase Edge Function for text optimization
// @ts-ignore: Deno types
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
// @ts-ignore: Supabase types
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0';
import {
  createErrorResponse,
  createSuccessResponse,
  parseRequestBody,
  ErrorCode,
  TokenUsage,
  createTokenUsage
} from '../_shared/utils.ts';

// Declare Deno types
declare const Deno: {
  env: {
    get(key: string): string | undefined;
  };
};

import { optimizeWithRequesty, REQUESTY_MODELS, RequestyModel } from './optimizeWithRequesty.ts';

// Service and model configurations
const SUPPORTED_MODELS = {
  requesty: [
    'llama-3-70b',
    'llama-3-8b',
    'claude-3.5-sonnet',
    'claude-3.7-sonnet',
    'claude-4',
    'claude-3.5-haiku',
    'deepseek-v3'
  ] as const
} as const;

type RequestyModelType = typeof SUPPORTED_MODELS.requesty[number];
type SupportedModel = RequestyModelType;

// Create a Supabase client for database operations
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Get service API keys from environment
const REQUESTY_API_KEY = Deno.env.get('REQUESTY_API_KEY') || '';

if (!REQUESTY_API_KEY) {
  throw new Error('Missing required service API keys');
}

// Add new type for pricing response
type PricingCheckResult = {
  can_use: boolean;
  pricing_model: 'credits' | 'payg' | null;
  cost: number;
  max_output_tokens?: string;
  error_code?: string;
};

// Helper function to estimate token count
function estimateTokenCount(text: string): number {
  // This is a simple estimation based on word count
  // Actual token count depends on the tokenizer used by the models
  return Math.ceil(text.split(/\s+/).length * 1.3);
}

// Helper function to get headers as an object
function getHeadersAsObject(headers: Headers): Record<string, string> {
  const obj: Record<string, string> = {};
  headers.forEach((value, key) => {
    obj[key] = value;
  });
  return obj;
}

serve(async (req: Request) => {
  try {
    // Parse the request body
    const {
      text,
      model,
      customPrompt,
      clientMessages,
      maxOutputTokens,
      debug = false,
      requestId
    } = await parseRequestBody(req);

    // Validate API key, extract user ID
    const apiKey = req.headers.get('Authorization')?.replace('Bearer ', '') || '';
    if (!apiKey) {
      return createErrorResponse(401, 'Missing API key', ErrorCode.UNAUTHORIZED);
    }

    // Validate VoiceHype API key and get user info
      const { data: validationData, error: validationError } = await supabase
        .rpc('validate_api_key', { p_key: apiKey });

    if (validationError || !validationData || validationData.length === 0) {
      console.error('API key validation failed:', {
        error: validationError,
        keyExists: !!validationData,
        keyHash: apiKey ? `${apiKey.substring(0, 3)}...${apiKey.substring(apiKey.length - 3)}` : 'none'
      });
      return createErrorResponse(401, 'Invalid API key', ErrorCode.INVALID_API_KEY);
    }

    const userId = validationData[0].user_id;
    const apiKeyId = validationData[0].api_key_id;

    // Validate required parameters
    if (!text) {
      return createErrorResponse(400, 'Missing required parameter: text', ErrorCode.INVALID_REQUEST);
    }

    if (!model) {
      return createErrorResponse(400, 'Missing required parameter: model', ErrorCode.INVALID_REQUEST);
    }

    // Validate model
    if (!Object.values(SUPPORTED_MODELS).some(models => models.includes(model as any))) {
      console.error('Unsupported model requested:', {
        model,
        supportedModels: SUPPORTED_MODELS,
        userId
      });
      return createErrorResponse(
        400,
        `Model '${model}' is not supported. Please choose from: ${Object.values(SUPPORTED_MODELS).flat().join(', ')}`,
        ErrorCode.UNSUPPORTED_MODEL
      );
    }

    // Detect model provider based on model name
    const modelProvider = 'requesty';

    // Estimate input token count for pricing
    const estimatedInputTokens = estimateTokenCount(text);
    console.log('Token estimation:', {
      text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
      estimatedInputTokens
    });

    console.log('Optimization request:', {
      model,
      modelProvider,
      textLength: text.length,
      customPromptProvided: !!customPrompt,
      customPromptLength: customPrompt?.length || 0,
      clientMessagesProvided: Array.isArray(clientMessages),
      maxOutputTokens,
      requestId: requestId || req.headers.get('X-Request-ID') || 'unknown',
      estimatedInputTokens
    });

    // Look up service pricing
    const { data: servicePricing, error: servicePricingError } = await supabase
      .from('service_pricing')
      .select('*')
      .eq('service', 'optimization')
      .eq('model', model)
      .eq('is_active', true)
      .single();

    // Check if we need to look up input/output pricing
    let hasInputOutputPricing = false;

    if (servicePricingError || !servicePricing) {
      // Determine the correct model names for input/output pricing
      let inputModel: string;
      let outputModel: string;

      if (modelProvider === 'requesty') {
        // For Requesty models, use the full URL mapping
        const requestyModel = REQUESTY_MODELS[model as RequestyModel];
        inputModel = `${requestyModel}/input`;
        outputModel = `${requestyModel}/output`;
      } else {
        // For other providers, use the original model name
        inputModel = `${model}/input`;
        outputModel = `${model}/output`;
      }

      // Check if we have input pricing
      const { data: inputPricing, error: inputPricingError } = await supabase
        .from('service_pricing')
        .select('*')
        .eq('service', 'optimization')
        .eq('model', inputModel)
        .eq('is_active', true)
        .single();

      // Check if we have output pricing
      const { data: outputPricing, error: outputPricingError } = await supabase
        .from('service_pricing')
        .select('*')
        .eq('service', 'optimization')
        .eq('model', outputModel)
        .eq('is_active', true)
        .single();

        // If we have both input and output pricing, we can proceed
        hasInputOutputPricing = !inputPricingError && inputPricing && !outputPricingError && outputPricing;

        console.log('Input/output pricing check:', {
          hasInputPricing: !inputPricingError && !!inputPricing,
          hasOutputPricing: !outputPricingError && !!outputPricing,
          hasInputOutputPricing,
          inputModel,
          outputModel,
          originalModel: model
        });
    }

    console.log('Service pricing lookup:', {
      pricing: servicePricing,
      error: servicePricingError,
      hasInputOutputPricing,
      query: {
        service: 'optimization',
        model: model,
        is_active: true
      }
    });

    // Check if service pricing exists (either base model or input/output format)
    if ((servicePricingError || !servicePricing) && !hasInputOutputPricing) {
      console.error('Service pricing not found:', {
        error: servicePricingError,
        model,
        hasInputOutputPricing,
        requestId: req.headers.get('X-Request-ID') || 'unknown'
      });
      return createErrorResponse(
        400,
        `Model '${model}' is not supported for optimization service. Please choose a supported model.`,
        ErrorCode.UNSUPPORTED_MODEL
      );
    }

    // Get user's credits and PAYG status
    const { data: userSettings, error: settingsError } = await supabase
      .from('user_settings')
      .select('payg_enabled')
      .eq('user_id', userId)
      .single();

    const { data: credits, error: creditsError } = await supabase
      .from('credits')
      .select('balance')
      .eq('user_id', userId)
      .single();

    console.log('User payment status:', {
      paygEnabled: userSettings?.payg_enabled,
      settingsError,
      credits: credits?.balance,
      creditsError,
      userId
    });

    // Check pricing allowance
    const { data: pricingCheck, error: pricingError } = await supabase
      .rpc('check_usage_allowance', {
        p_user_id: userId,
        p_service: 'optimization',
        p_model: model,
        p_amount: estimatedInputTokens,
        p_api_key_id: apiKeyId,
        p_is_input_only: true // Specify that we're only checking for input tokens initially
      }) as { data: PricingCheckResult[] | null, error: any };

    // Log all pricing-related information
    console.log('Complete pricing check details:', {
      check: pricingCheck,
      error: pricingError,
      requestParams: {
        userId,
        service: 'optimization',
        model,
        estimatedInputTokens
      },
      servicePricing: {
        found: !!servicePricing,
        costPerUnit: servicePricing?.cost_per_unit,
        error: servicePricingError
      },
      userStatus: {
        paygEnabled: userSettings?.payg_enabled,
        credits: credits?.balance,
        settingsError,
        creditsError
      },
      estimatedCost: servicePricing ? servicePricing.cost_per_unit * estimatedInputTokens : null,
      maxOutputTokens: pricingCheck?.[0]?.max_output_tokens
    });

    if (pricingError || !pricingCheck || !pricingCheck[0] || !pricingCheck[0].can_use) {
      const errorDetails = {
        pricingError,
        check: pricingCheck,
        estimatedCost: servicePricing ? servicePricing.cost_per_unit * estimatedInputTokens : null,
        availableCredits: credits?.balance,
        paygEnabled: userSettings?.payg_enabled,
        servicePricingFound: !!servicePricing,
        model,
        estimatedInputTokens,
        errorCode: pricingCheck?.[0]?.error_code
      };

      console.error('Pricing check failed:', errorDetails);

      // Check for specific error code
      if (pricingCheck?.[0]?.error_code === 'unpaid_balance') {
        // Get detailed unpaid balance information
        const { data: unpaidBalances, error: unpaidError } = await supabase
          .rpc('get_unpaid_payg_balances', { p_user_id: userId });

        console.log('Unpaid balances:', {
          balances: unpaidBalances,
          error: unpaidError
        });

        return createErrorResponse(
          403,
          'You have an unpaid balance. Please settle your outstanding balance before using this service.',
          ErrorCode.UNPAID_BALANCE,
          { unpaid_balance: unpaidBalances }
        );
      }

      if (pricingCheck?.[0]?.error_code === 'insufficient_credits') {
        return createErrorResponse(
          403,
          'Insufficient credits to perform this operation.',
          ErrorCode.INSUFFICIENT_CREDITS,
          { available_credits: credits?.balance }
        );
      }

      if (pricingCheck?.[0]?.error_code === 'payg_disabled') {
        return createErrorResponse(
          403,
          'Pay-as-you-go is not enabled for your account.',
          ErrorCode.INSUFFICIENT_CREDITS
        );
      }

      return createErrorResponse(
        403,
        'Unable to process request due to pricing constraints.',
        ErrorCode.SERVICE_ERROR
      );
    }

    // Calculate the estimated cost
    const estimatedCost = servicePricing ? servicePricing.cost_per_unit * estimatedInputTokens : 0;

    // Record pending usage
    await supabase
      .from('usage_history')
      .insert({
        user_id: userId,
        api_key_id: apiKeyId,
        service: 'optimization',
        model: model,
        amount: estimatedInputTokens,
        cost: estimatedCost,
        pricing_model: pricingCheck[0].pricing_model,
        status: 'pending',
        metadata: {
          inputLength: text.length,
          inputTokens: estimatedInputTokens,
          outputLength: 0,
          outputTokens: 0
        }
      });

    // Default max tokens from pricing check if not provided
    const resolvedMaxOutputTokens = maxOutputTokens || parseInt(pricingCheck?.[0]?.max_output_tokens || '4096');

    try {
      // Perform optimization based on the model provider
      let optimizedText: string;
      let tokenUsage: TokenUsage;
      let modelUsed: string = model; // Keep track of the actual model used

      if (modelProvider === 'requesty') {
        // Use Requesty
        const result = await optimizeWithRequesty(text, model as RequestyModel, customPrompt, resolvedMaxOutputTokens, REQUESTY_API_KEY, clientMessages);
        if ('modelUsed' in result && result.modelUsed) {
          modelUsed = result.modelUsed;
        }
        optimizedText = result.optimizedText;
        tokenUsage = result.tokenUsage;
      } else {
        throw new Error(`Unsupported model provider: ${modelProvider}`);
      }

      console.log('Optimization completed with actual token counts:', {
        ...tokenUsage,
        inputLength: text.length,
        outputLength: optimizedText.length,
        model
      });

      // Get pricing for input and output tokens
      // Use the full Requesty URL for model pricing lookups
      let inputModel: string;
      let outputModel: string;

      if (modelProvider === 'requesty') {
        // For Requesty models, use the full URL mapping
        const requestyModel = REQUESTY_MODELS[model as RequestyModel];
        inputModel = `${requestyModel}/input`;
        outputModel = `${requestyModel}/output`;
      } else {
        // For other providers, use the original model name
        inputModel = `${model}/input`;
        outputModel = `${model}/output`;
      }

      console.log('Looking up pricing with models:', {
        inputModel,
        outputModel,
        originalModel: model,
        provider: modelProvider
      });

      const { data: inputPricing, error: inputPricingError } = await supabase
        .from('service_pricing')
        .select('cost_per_unit')
        .eq('service', 'optimization')
        .eq('model', inputModel)
        .eq('is_active', true)
        .single();

      const { data: outputPricing, error: outputPricingError } = await supabase
        .from('service_pricing')
        .select('cost_per_unit')
        .eq('service', 'optimization')
        .eq('model', outputModel)
        .eq('is_active', true)
        .single();

      // Calculate total cost based on input and output tokens
      let totalCost = 0;
      if (inputPricing && outputPricing) {
        // If we have separate pricing for input and output
        totalCost = (inputPricing.cost_per_unit * tokenUsage.input_tokens) +
                    (outputPricing.cost_per_unit * tokenUsage.output_tokens);
      } else {
        // Fallback to original pricing if separate pricing not found
        totalCost = servicePricing.cost_per_unit * tokenUsage.total_tokens;
      }

      console.log('Cost calculation with actual tokens:', {
        tokenUsage,
        inputCost: inputPricing?.cost_per_unit,
        outputCost: outputPricing?.cost_per_unit,
        totalCost,
        fallbackUsed: !inputPricing || !outputPricing
      });

      // Record successful usage and update balances with actual token count
      const { error: finalizeError } = await supabase
        .rpc('finalize_usage', {
          p_user_id: userId,
          p_service: 'optimization',
          p_model: model,
          p_amount: tokenUsage.total_tokens,
          p_cost: Number(totalCost.toFixed(9)), // Use calculated cost based on input/output pricing
          p_pricing_model: pricingCheck[0].pricing_model,
          p_metadata: {
            inputLength: text.length,
            inputTokens: tokenUsage.input_tokens,
            outputLength: optimizedText.length,
            outputTokens: tokenUsage.output_tokens
          }
        });

      if (finalizeError) {
        console.error('Failed to finalize usage:', finalizeError);
        // Continue anyway as the optimization was successful
      }

      // Handle all possible response formats with proper regex parsing
      let processedText = optimizedText;
      
      // First try JSON parsing for structured responses
      try {
        if (optimizedText.trim().startsWith('{') && (optimizedText.includes('"optimizedText"') || optimizedText.includes('"commandResult"'))) {
          const parsedJson = JSON.parse(optimizedText);

          // Case 1: Direct commandResult property (voice commands)
          if (parsedJson.commandResult && typeof parsedJson.commandResult === 'string') {
            console.log('Detected JSON response with commandResult field');
            processedText = parsedJson.commandResult;
          }
          // Case 2: Direct optimizedText property
          else if (parsedJson.optimizedText && typeof parsedJson.optimizedText === 'string') {
            console.log('Detected JSON response with optimizedText field');
            processedText = parsedJson.optimizedText;
          }
          // Case 3: Nested data structure
          else if (parsedJson.data?.commandResult) {
            console.log('Detected nested JSON response with commandResult');
            processedText = parsedJson.data.commandResult;
          }
          else if (parsedJson.data?.optimizedText) {
            console.log('Detected nested JSON response with optimizedText');
            processedText = typeof parsedJson.data.optimizedText === 'string' 
              ? parsedJson.data.optimizedText 
              : parsedJson.data.optimizedText.optimizedText || optimizedText;
          }
        }

        // If we still have JSON-like content, try additional regex parsing
        if (processedText.includes('"optimizedText"') || processedText.includes('"commandResult"')) {
          // Try to extract content between quotes after optimizedText or commandResult
          const regex = /"(?:optimizedText|commandResult)"\s*:\s*"((?:[^"\\]|\\.)*)"/;
          const match = processedText.match(regex);
          if (match && match[1]) {
            console.log('Extracted content using regex pattern');
            processedText = JSON.parse(`"${match[1]}"`); // Parse to handle escaped characters
          }
        }

      } catch (e) {
        console.log('Response format parsing error:', e);
        // Keep the original optimized text if parsing fails
        // This ensures we don't lose the content even if parsing fails
      }

      // Final validation to ensure we have valid content
      if (!processedText || typeof processedText !== 'string') {
        console.log('Invalid processed text, falling back to original response');
        processedText = optimizedText;
      }

      // Create the response data
      const responseData = {
        optimizedText: processedText,
        metadata: {
          model,
          modelUsed, // Include the actual model used (could be different if fallback was used)
          inputLength: text.length,
          outputLength: processedText.length,
          usage: tokenUsage
        }
      };

      // Log the response for debugging
      console.log('Sending successful response:', {
        model,
        modelUsed,
        modelProvider,
        customPromptUsed: !!customPrompt,
        inputLength: text.length,
        outputLength: processedText.length,
        tokenUsage: {
          input: tokenUsage.input_tokens,
          output: tokenUsage.output_tokens,
          total: tokenUsage.total_tokens
        },
        optimizedPreview: processedText
      });

      return createSuccessResponse(responseData);

    } catch (error: any) {
      // Record failed usage
      await supabase
        .from('usage_history')
        .insert({
          user_id: userId,
          api_key_id: apiKeyId,
          service: 'optimization',
          model: model,
          amount: estimatedInputTokens,
          cost: estimatedCost,
          pricing_model: pricingCheck[0].pricing_model,
          status: 'failed',
          metadata: {
            inputLength: text.length,
            inputTokens: estimatedInputTokens,
            outputLength: 0,
            outputTokens: 0
          }
        });

      console.error('Optimization error:', {
        message: error.message,
        stack: error.stack,
        model,
        textLength: text.length
      });

      return createErrorResponse(
        500,
        `Optimization failed: ${error.message}`,
        ErrorCode.SERVICE_ERROR
      );
    }

  } catch (error: any) {
    console.error('Unexpected error:', {
      message: error.message,
      stack: error.stack
    });

    return createErrorResponse(
      500,
      `Server error: ${error.message}`,
      ErrorCode.SERVICE_ERROR
    );
  }
});