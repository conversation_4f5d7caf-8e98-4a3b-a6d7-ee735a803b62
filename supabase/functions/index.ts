// Supabase Edge Function for text optimization
// @ts-ignore: Deno types
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
// @ts-ignore: Supabase types
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0';
import {
  createErrorResponse,
  createSuccessResponse,
  parseRequestBody,
  ErrorCode,
  TokenUsage,
  createTokenUsage
} from '../_shared/utils.ts';

// Declare Deno types
declare const Deno: {
  env: {
    get(key: string): string | undefined;
  };
};

import { optimizeWithRequesty, REQUESTY_MODELS, RequestyModel } from './optimize/optimizeWithRequesty.ts';

// Service and model configurations
const SUPPORTED_MODELS = {
  replicate: [
    'anthropic/claude-3.7-sonnet',
    'anthropic/claude-3.5-sonnet',
    'anthropic/claude-3.5-haiku',
    'meta/meta-llama-3-70b-instruct',
    'meta/meta-llama-3.1-405b-instruct',
    'deepseek-ai/deepseek-r1'
  ] as const,
  openai: [
    'gpt-4o',
    'gpt-4o-mini',
    'o3-mini'
  ] as const,
  requesty: [
    'claude-4-sonnet',
    'claude-3.7-sonnet',
    'claude-3.5-sonnet',
    'claude-haiku',
    'llama-3.1-70b',
    'llama-3.1-8b-instruct-max',
    'deepseek-v3'
  ] as const
} as const;

type ReplicateModel = typeof SUPPORTED_MODELS.replicate[number];
type OpenAIModel = typeof SUPPORTED_MODELS.openai[number];
type RequestyModelType = typeof SUPPORTED_MODELS.requesty[number];
type SupportedModel = ReplicateModel | OpenAIModel | RequestyModelType;

// Default optimization prompt
const DEFAULT_OPTIMIZATION_PROMPT = `You are a helpful assistant whose job is to optimize a transcript for clarity and actionability.

Your task:
1. Improve the structure and flow while preserving all important details
2. Fix grammar, remove verbal fillers (like, um, uh, you know)
3. Clean up false starts and repetitive phrasing
4. Format the result in a clear, well-structured way
5. Maintain all explanations, instructions, and technical details provided by the user
6. If the user explains concepts or provides reasoning, preserve that context
7. Make sure the optimized text is detailed and comprehensive, not overly simplified
8. Format the text as if creating a written document or message that someone could immediately act upon

Do NOT:
- Oversimplify or remove important context
- Eliminate explanations provided by the user
- Summarize so aggressively that details are lost

Your goal is to create a polished, professional version of what the user intended to say, not a brief summary.`;

// Create a Supabase client for database operations
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Get service API keys from environment
const REPLICATE_API_TOKEN = Deno.env.get('REPLICATE_API_TOKEN') || '';
const OPENAI_API_KEY = Deno.env.get('OPENAI_API_KEY') || '';
const OPENROUTER_API_KEY = Deno.env.get('OPENROUTER_API_KEY') || '';

if (!OPENAI_API_KEY && !OPENROUTER_API_KEY) {
  throw new Error('Missing required service API keys');
}

// Add new type for pricing response
type PricingCheckResult = {
  can_use: boolean;
  pricing_model: 'credits' | 'payg' | null;
  cost: number;
  max_output_tokens?: string;
  error_code?: string;
};

// Helper function to estimate token count
function estimateTokenCount(text: string): number {
  // Rough estimation: ~4 characters per token for English text
  return Math.ceil(text.length / 4);
}

// Function to get model provider
function getModelProvider(model: string): 'replicate' | 'openai' | 'requesty' | null {
  if (SUPPORTED_MODELS.replicate.includes(model as ReplicateModel)) {
    return 'replicate';
  } else if (SUPPORTED_MODELS.openai.includes(model as OpenAIModel)) {
    return 'openai';
  } else if (SUPPORTED_MODELS.requesty.includes(model as RequestyModelType)) {
    return 'requesty';
  }
  return null;
}

// Function for OpenAI optimization
async function optimizeWithOpenAI(
  text: string,
  model: OpenAIModel,
  customPrompt: string = DEFAULT_OPTIMIZATION_PROMPT,
  maxOutputTokens?: number
): Promise<{ optimizedText: string, tokenUsage: TokenUsage }> {
  console.log('Optimizing with OpenAI:', {
    model,
    textLength: text.length,
    promptLength: customPrompt.length,
    maxOutputTokens,
    maxOutputTokensType: typeof maxOutputTokens,
    usingDefaultPrompt: customPrompt === DEFAULT_OPTIMIZATION_PROMPT,
    customPromptPreview: customPrompt !== DEFAULT_OPTIMIZATION_PROMPT
      ? `${customPrompt.substring(0, 100)}...`
      : '(using default)'
  });

  // Ensure maxOutputTokens is a valid integer
  const validatedMaxTokens = maxOutputTokens && !isNaN(maxOutputTokens) ?
    Math.floor(maxOutputTokens) : 4096;

  // Create the prompt for the model
  const prompt = `${customPrompt}\n\nTranscript to optimize: ${text}`;

  // Prepare the request body (log it for debugging)
  const requestBody = {
    model: model,
    messages: [
      {
        role: 'system',
        content: customPrompt
      },
      {
        role: 'user',
        content: text
      }
    ],
    max_tokens: validatedMaxTokens,
    temperature: 0.7
  };

  console.log('OpenAI request structure:', {
    endpoint: 'https://api.openai.com/v1/chat/completions',
    modelUsed: model,
    messageCount: requestBody.messages.length,
    systemMessageLength: customPrompt.length,
    userMessageLength: text.length,
    maxTokens: validatedMaxTokens
  });

  // Prepare the API request
  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${OPENAI_API_KEY}`
    },
    body: JSON.stringify(requestBody)
  });

  const responseHeaders = getHeadersAsObject(response.headers);
  console.log('OpenAI response headers:', responseHeaders);

  if (!response.ok) {
    const errorText = await response.text();
    console.error('OpenAI API error:', {
      status: response.status,
      statusText: response.statusText,
      body: errorText
    });
    throw new Error(`OpenAI API error: ${response.status} - ${response.statusText}. Response: ${errorText}`);
  }

  const data = await response.json();
  console.log('OpenAI response:', {
    usage: data.usage,
    modelUsed: data.model,
    finishReason: data.choices[0].finish_reason,
    contentLength: data.choices[0].message.content.length
  });

  // Create standardized token usage
  const tokenUsage = createTokenUsage(
    data.usage.prompt_tokens,
    data.usage.completion_tokens,
    {
      input_details: { prompt_tokens: data.usage.prompt_tokens },
      output_details: { completion_tokens: data.usage.completion_tokens }
    }
  );

  return {
    optimizedText: data.choices[0].message.content.trim(),
    tokenUsage
  };
}

// Function for Replicate optimization
async function optimizeWithReplicate(
  text: string,
  model: ReplicateModel,
  customPrompt: string = DEFAULT_OPTIMIZATION_PROMPT,
  maxOutputTokens?: number
): Promise<{ optimizedText: string, tokenUsage: TokenUsage }> {
  console.log('Optimizing with Replicate:', {
    model,
    textLength: text.length,
    promptLength: customPrompt.length,
    maxOutputTokens,
    maxOutputTokensType: typeof maxOutputTokens
  });

  // Ensure maxOutputTokens is a valid integer
  const validatedMaxTokens = maxOutputTokens && !isNaN(maxOutputTokens) ?
    Math.floor(maxOutputTokens) : 4096;

  // Create the prompt for the model
  const prompt = `${customPrompt}\n\nTranscript to optimize: ${text}`;

  // Extract model name and version from the model string (e.g., "anthropic/claude-3.5-haiku")
  const [provider, modelName] = model.split('/');

  // Prepare the API request based on the model
  let requestBody: any = {
    input: {
      prompt: prompt,
      max_tokens: validatedMaxTokens,
      temperature: 0.7
    }
  };

  // Log the request body for debugging
  console.log('Replicate request body:', {
    modelName,
    maxTokensValue: validatedMaxTokens,
    maxTokensType: typeof validatedMaxTokens
  });

  // Adjust input parameters based on model type
  if (model.startsWith('anthropic/claude')) {
    requestBody.input = {
      prompt: prompt,
      max_tokens: validatedMaxTokens,
      temperature: 0.7,
      system: customPrompt // Use system parameter for Claude models
    };
  } else if (model.startsWith('meta/meta-llama')) {
    requestBody.input = {
      prompt: prompt,
      max_tokens: validatedMaxTokens,
      temperature: 0.7
    };
  } else if (model.startsWith('deepseek-ai/deepseek')) {
    requestBody.input = {
      prompt: prompt,
      max_tokens: validatedMaxTokens,
      temperature: 0.7
    };
  }

  console.log('Replicate request:', {
    url: `https://api.replicate.com/v1/models/${model}/predictions`,
    body: requestBody
  });

  // Create prediction with direct model endpoint
  const createResponse = await fetch(`https://api.replicate.com/v1/models/${model}/predictions`, {
    method: 'POST',
      headers: {
      'Authorization': `Token ${REPLICATE_API_TOKEN}`,
      'Content-Type': 'application/json',
      'Prefer': 'wait' // Wait for the prediction to complete
    },
    body: JSON.stringify(requestBody)
  });

  if (!createResponse.ok) {
    const errorText = await createResponse.text();
    console.error('Replicate API error:', {
      status: createResponse.status,
      statusText: createResponse.statusText,
      body: errorText
    });
    throw new Error(`Replicate API error: ${createResponse.status} - ${createResponse.statusText}. Response: ${errorText}`);
  }

  const result = await createResponse.json();
  console.log('Replicate response:', {
    id: result.id,
    status: result.status,
    metrics: result.metrics,
    output: typeof result.output === 'string' ? result.output.substring(0, 100) + '...' : result.output
  });

  let optimizedText = '';
  if (result.output) {
    optimizedText = Array.isArray(result.output) ? result.output.join('') : result.output;
  } else if (result.error) {
    throw new Error(`Optimization error: ${result.error}`);
  } else {
    throw new Error('No output received from Replicate API');
  }

  // Extract actual token usage from the response using standardized schema
  const inputTokens = result.metrics?.input_token_count || estimateTokenCount(prompt);
  const outputTokens = result.metrics?.output_token_count || estimateTokenCount(optimizedText);

  const tokenUsage = createTokenUsage(
    inputTokens,
    outputTokens,
    {
      input_details: { estimated: !result.metrics?.input_token_count },
      output_details: { estimated: !result.metrics?.output_token_count }
    }
  );

  return {
    optimizedText: optimizedText.trim(),
    tokenUsage
  };
}

function getHeadersAsObject(headers: Headers): Record<string, string> {
  const obj: Record<string, string> = {};
  headers.forEach((value, key) => {
    obj[key] = value;
  });
  return obj;
}

// Main handler function
serve(async (req: Request) => {
  try {
    // Get VoiceHype API key from header
    let voiceHypeApiKey = req.headers.get('apikey');
    if (!voiceHypeApiKey) {
      const authHeader = req.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer')) {
        voiceHypeApiKey = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    if (!voiceHypeApiKey) {
      const headers = getHeadersAsObject(req.headers);
      console.error('Missing API key:', { headers });
      return createErrorResponse(401, 'Missing API key', ErrorCode.INVALID_API_KEY);
    }

    console.log('Attempting to validate API key:', {
      keyLength: voiceHypeApiKey.length,
      keyPrefix: voiceHypeApiKey.substring(0, 10) + '...',
    });

    // Validate VoiceHype API key and get user info
    const { data: validationData, error: validationError } = await supabase
      .rpc('validate_api_key', { p_key: voiceHypeApiKey });

    console.log('Validation response:', {
      data: validationData,
      error: validationError,
      hasData: !!validationData,
      dataLength: validationData?.length
    });

    if (validationError || !validationData || validationData.length === 0) {
      const headers = getHeadersAsObject(req.headers);
      console.error('API key validation failed:', {
        error: validationError,
        data: validationData,
        requestHeaders: headers
      });
      return createErrorResponse(401, 'Invalid API key', ErrorCode.INVALID_API_KEY);
    }

    const userId = validationData[0].user_id;
    const apiKeyId = validationData[0].api_key_id;

    console.log('API key validated successfully:', {
      userId,
      apiKeyId
    });

    // Parse request body
    const body = await parseRequestBody(req);
    const {
      text,
      model = 'gpt-4o-mini',
      customPrompt = DEFAULT_OPTIMIZATION_PROMPT
    } = body;

    // Log the complete request details for debugging
    console.log('Complete request details:', {
      headers: getHeadersAsObject(req.headers),
      model,
      customPromptProvided: customPrompt !== DEFAULT_OPTIMIZATION_PROMPT,
      customPromptLength: customPrompt.length,
      customPromptPreview: customPrompt !== DEFAULT_OPTIMIZATION_PROMPT
        ? `${customPrompt.substring(0, 100)}...`
        : '(using default)',
      textLength: text?.length,
      textPreview: text ? `${text.substring(0, 100)}...` : 'No text provided'
    });

    // Validate request parameters
    if (!text) {
      return createErrorResponse(400, 'Missing text to optimize', ErrorCode.INVALID_REQUEST);
    }

    const modelProvider = getModelProvider(model);
    if (!modelProvider) {
      const allModels = [
        ...SUPPORTED_MODELS.replicate,
        ...SUPPORTED_MODELS.openai,
        ...SUPPORTED_MODELS.requesty
      ];
      return createErrorResponse(400, `Unsupported model. Must be one of: ${allModels.join(', ')}`, ErrorCode.UNSUPPORTED_MODEL);
    }

    // Estimate token count for input text
    const estimatedInputTokens = estimateTokenCount(text);
    console.log('Token estimation:', {
      text: text.substring(0, 100) + '...',
      estimatedInputTokens,
      textLength: text.length
    });

    // Get service pricing details
    const { data: servicePricing, error: servicePricingError } = await supabase
      .from('service_pricing')
      .select('*')
      .eq('service', 'optimization')
      .eq('model', model)
      .eq('is_active', true)
      .single();

    // If we don't find the base model, check if we have input/output pricing instead
    let hasInputOutputPricing = false;
    if (servicePricingError || !servicePricing) {
      // Determine the correct model names for input/output pricing
      let inputModel: string;
      let outputModel: string;

      if (modelProvider === 'requesty') {
        // For Requesty models, use the full URL mapping
        const requestyModel = REQUESTY_MODELS[model as RequestyModelType];
        inputModel = `${requestyModel}/input`;
        outputModel = `${requestyModel}/output`;
      } else {
        // For other providers, use the original model name
        inputModel = `${model}/input`;
        outputModel = `${model}/output`;
      }

      // Check if we have input pricing
      const { data: inputPricing, error: inputPricingError } = await supabase
        .from('service_pricing')
        .select('*')
        .eq('service', 'optimization')
        .eq('model', inputModel)
        .eq('is_active', true)
        .single();

      // Check if we have output pricing
      const { data: outputPricing, error: outputPricingError } = await supabase
        .from('service_pricing')
        .select('*')
        .eq('service', 'optimization')
        .eq('model', outputModel)
        .eq('is_active', true)
        .single();

        // If we have both input and output pricing, we can proceed
        hasInputOutputPricing = !inputPricingError && inputPricing && !outputPricingError && outputPricing;

        console.log('Input/output pricing check:', {
          hasInputPricing: !inputPricingError && !!inputPricing,
          hasOutputPricing: !outputPricingError && !!outputPricing,
          hasInputOutputPricing,
          inputModel,
          outputModel,
          originalModel: model
        });
    }

    console.log('Service pricing lookup:', {
      pricing: servicePricing,
      error: servicePricingError,
      hasInputOutputPricing,
      query: {
        service: 'optimization',
        model: model,
        is_active: true
      }
    });

    // Check if service pricing exists (either base model or input/output format)
    if ((servicePricingError || !servicePricing) && !hasInputOutputPricing) {
      console.error('Service pricing not found:', {
        error: servicePricingError,
        model,
        hasInputOutputPricing,
        requestId: req.headers.get('X-Request-ID') || 'unknown'
      });
      return createErrorResponse(
        400,
        `Model '${model}' is not supported for optimization service. Please choose a supported model.`,
        ErrorCode.UNSUPPORTED_MODEL
      );
    }

    // Get user's credits and PAYG status
    const { data: userSettings, error: settingsError } = await supabase
      .from('user_settings')
      .select('payg_enabled')
      .eq('user_id', userId)
      .single();

    const { data: credits, error: creditsError } = await supabase
      .from('credits')
      .select('balance')
      .eq('user_id', userId)
      .single();

    console.log('User payment status:', {
      paygEnabled: userSettings?.payg_enabled,
      settingsError,
      credits: credits?.balance,
      creditsError,
      userId
    });

    // Check pricing allowance
    const { data: pricingCheck, error: pricingError } = await supabase
      .rpc('check_usage_allowance', {
        p_user_id: userId,
        p_service: 'optimization',
        p_model: model,
        p_amount: estimatedInputTokens,
        p_api_key_id: apiKeyId,
        p_is_input_only: true // Specify that we're only checking for input tokens initially
      }) as { data: PricingCheckResult[] | null, error: any };

    // Log all pricing-related information
    console.log('Complete pricing check details:', {
      check: pricingCheck,
      error: pricingError,
      requestParams: {
        userId,
        service: 'optimization',
        model,
        estimatedInputTokens
      },
      servicePricing: {
        found: !!servicePricing,
        costPerUnit: servicePricing?.cost_per_unit,
        error: servicePricingError
      },
      userStatus: {
        paygEnabled: userSettings?.payg_enabled,
        credits: credits?.balance,
        settingsError,
        creditsError
      },
      estimatedCost: servicePricing ? servicePricing.cost_per_unit * estimatedInputTokens : null,
      maxOutputTokens: pricingCheck?.[0]?.max_output_tokens
    });

    if (pricingError || !pricingCheck || !pricingCheck[0] || !pricingCheck[0].can_use) {
      const errorDetails = {
        pricingError,
        check: pricingCheck,
        estimatedCost: servicePricing ? servicePricing.cost_per_unit * estimatedInputTokens : null,
        availableCredits: credits?.balance,
        paygEnabled: userSettings?.payg_enabled,
        servicePricingFound: !!servicePricing,
        model,
        estimatedInputTokens,
        errorCode: pricingCheck?.[0]?.error_code
      };

      console.error('Pricing check failed:', errorDetails);

      // Check for specific error code
      if (pricingCheck?.[0]?.error_code === 'unpaid_balance') {
        // Get detailed unpaid balance information
        const { data: unpaidBalances, error: unpaidError } = await supabase
          .rpc('get_unpaid_payg_balances', { p_user_id: userId });

        console.log('Unpaid balances:', {
          balances: unpaidBalances,
          error: unpaidError
        });

        if (!unpaidError && unpaidBalances && unpaidBalances.length > 0) {
          // Format the unpaid balances for display
          const formattedBalances = unpaidBalances.map(balance => {
            const date = new Date(balance.month);
            return {
              month: date.toLocaleString('en-US', { month: 'long', year: 'numeric' }),
              amount: Number(balance.total_amount).toFixed(2),
              id: balance.id,
              status: balance.payment_status,
              metadata: balance.payment_metadata
            };
          });

          return createErrorResponse(
            402,
            'You have unpaid PAYG balances from previous months. Please settle your outstanding balance before using the service.',
            ErrorCode.UNPAID_BALANCE,
            { unpaidBalances: formattedBalances }
          );
        } else {
          return createErrorResponse(
            402,
            'You have unpaid PAYG balances from previous months. Please settle your outstanding balance before using the service.',
            ErrorCode.UNPAID_BALANCE
          );
        }
      }

      return createErrorResponse(
        402,
        'Insufficient credits or PAYG not enabled',
        ErrorCode.INSUFFICIENT_CREDITS
      );
    }

    // Ensure cost is not null
    const estimatedCost = pricingCheck[0].cost || (servicePricing ? servicePricing.cost_per_unit * estimatedInputTokens : 0);

    // Record pending usage
    const { error: usageError } = await supabase
      .from('usage_history')
      .insert({
        user_id: userId,
        api_key_id: apiKeyId,
        service: 'optimization',
        model: model,
        amount: estimatedInputTokens,
        cost: estimatedCost, // Use calculated cost instead of potentially null value
        pricing_model: pricingCheck[0].pricing_model,
        status: 'pending',
        metadata: {
          inputLength: text.length,
          inputTokens: estimatedInputTokens,
          outputLength: 0,
          outputTokens: 0
        }
      });

    if (usageError) {
      console.error('Failed to record usage - detailed error:', {
        error: usageError,
        errorMessage: usageError.message,
        errorDetails: usageError.details,
        errorHint: usageError.hint,
        errorCode: usageError.code,
        requestId: req.headers.get('X-Request-ID') || 'unknown',
        insertData: {
          userId,
          apiKeyId,
          service: 'optimization',
      model,
          amount: estimatedInputTokens,
          cost: estimatedCost,
          pricingModel: pricingCheck[0].pricing_model
        }
      });
      return createErrorResponse(500, `Failed to record usage: ${usageError.message || 'Unknown error'}`, ErrorCode.SERVICE_ERROR);
    }

    // Perform optimization based on model provider
    let optimizedText: string;
    let tokenUsage: TokenUsage;
    let modelUsed = model; // Track the actual model used (for fallbacks)

    try {
      let result: { optimizedText: string, tokenUsage: TokenUsage, modelUsed?: string };

      // Get max output tokens from pricing check
      const maxOutputTokens = pricingCheck?.[0]?.max_output_tokens ?
        parseInt(pricingCheck[0].max_output_tokens, 10) : undefined;

      if (modelProvider === 'openai') {
        result = await optimizeWithOpenAI(text, model as OpenAIModel, customPrompt, maxOutputTokens);
      } else if (modelProvider === 'replicate') {
        result = await optimizeWithReplicate(text, model as ReplicateModel, customPrompt, maxOutputTokens);
      } else if (modelProvider === 'requesty') {
        // Use Requesty
        result = await optimizeWithRequesty(text, model as RequestyModelType, customPrompt, maxOutputTokens, REQUESTY_API_KEY);

        // If modelUsed is returned, update it
        if (result.modelUsed) {
          modelUsed = result.modelUsed;
        }
      } else {
        throw new Error(`Unsupported model provider: ${modelProvider}`);
      }

      optimizedText = result.optimizedText;
      tokenUsage = result.tokenUsage;

      console.log('Optimization completed with actual token counts:', {
        ...tokenUsage,
        inputLength: text.length,
        outputLength: optimizedText.length,
        model
      });

      // Get pricing for input and output tokens
      // Use the full OpenRouter URL for model pricing lookups
      let inputModel: string;
      let outputModel: string;

      if (modelProvider === 'requesty') {
        // For Requesty models, use the full URL mapping
        const requestyModel = REQUESTY_MODELS[model as RequestyModelType];
        inputModel = `${requestyModel}/input`;
        outputModel = `${requestyModel}/output`;
      } else {
        // For other providers, use the original model name
        inputModel = `${model}/input`;
        outputModel = `${model}/output`;
      }

      console.log('Looking up pricing with models:', {
        inputModel,
        outputModel,
        originalModel: model,
        provider: modelProvider
      });

      const { data: inputPricing, error: inputPricingError } = await supabase
        .from('service_pricing')
        .select('cost_per_unit')
        .eq('service', 'optimization')
        .eq('model', inputModel)
        .eq('is_active', true)
        .single();

      const { data: outputPricing, error: outputPricingError } = await supabase
        .from('service_pricing')
        .select('cost_per_unit')
        .eq('service', 'optimization')
        .eq('model', outputModel)
        .eq('is_active', true)
        .single();

      // Calculate total cost based on input and output tokens
      let totalCost = 0;
      if (inputPricing && outputPricing) {
        // If we have separate pricing for input and output
        totalCost = (inputPricing.cost_per_unit * tokenUsage.input_tokens) +
                    (outputPricing.cost_per_unit * tokenUsage.output_tokens);
      } else {
        // Fallback to original pricing if separate pricing not found
        totalCost = servicePricing.cost_per_unit * tokenUsage.total_tokens;
      }

      console.log('Cost calculation with actual tokens:', {
        tokenUsage,
        inputCost: inputPricing?.cost_per_unit,
        outputCost: outputPricing?.cost_per_unit,
        totalCost,
        fallbackUsed: !inputPricing || !outputPricing
      });

      // Record successful usage and update balances with actual token count
      const { error: finalizeError } = await supabase
        .rpc('finalize_usage', {
          p_user_id: userId,
          p_service: 'optimization',
          p_model: model,
          p_amount: tokenUsage.total_tokens,
          p_cost: Number(totalCost.toFixed(9)), // Use calculated cost based on input/output pricing
          p_pricing_model: pricingCheck[0].pricing_model,
          p_metadata: {
            inputLength: text.length,
            inputTokens: tokenUsage.input_tokens,
            outputLength: optimizedText.length,
            outputTokens: tokenUsage.output_tokens
          }
        });

      if (finalizeError) {
        console.error('Failed to finalize usage:', finalizeError);
        // Continue anyway as the optimization was successful
      }

      // Create the response data
      const responseData = {
        optimizedText,
        metadata: {
          model,
          modelUsed, // Include the actual model used (could be different if fallback was used)
          inputLength: text.length,
          outputLength: optimizedText.length,
          usage: tokenUsage
        }
      };

      // Log the response for debugging
      console.log('Sending successful response:', {
        model,
        modelUsed,
        modelProvider,
        customPromptUsed: customPrompt !== DEFAULT_OPTIMIZATION_PROMPT,
        inputLength: text.length,
        outputLength: optimizedText.length,
        tokenUsage: {
          input: tokenUsage.input_tokens,
          output: tokenUsage.output_tokens,
          total: tokenUsage.total_tokens
        },
        optimizedPreview: optimizedText.substring(0, 100) + '...'
      });

      return createSuccessResponse(responseData);

    } catch (error: any) {
      // Record failed usage
      await supabase
        .from('usage_history')
        .insert({
          user_id: userId,
          api_key_id: apiKeyId,
          service: 'optimization',
          model: model,
          amount: estimatedInputTokens,
          cost: estimatedCost,
          pricing_model: pricingCheck[0].pricing_model,
          status: 'failed',
          metadata: {
            inputLength: text.length,
            inputTokens: estimatedInputTokens,
            outputLength: 0,
            outputTokens: 0
          }
        });

      console.error('Optimization error:', {
        message: error.message,
        stack: error.stack,
        model,
        textLength: text.length
      });

      return createErrorResponse(
        500,
        `Optimization failed: ${error.message}`,
        ErrorCode.SERVICE_ERROR
      );
    }

  } catch (error: any) {
    console.error('Unexpected error:', {
      message: error.message,
      stack: error.stack
    });

    return createErrorResponse(
      500,
      `Server error: ${error.message}`,
      ErrorCode.SERVICE_ERROR
    );
  }
});