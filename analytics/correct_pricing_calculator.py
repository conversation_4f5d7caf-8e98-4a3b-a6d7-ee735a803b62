#!/usr/bin/env python3
"""
VoiceHype Correct Pricing Calculator
Bismillahir rahmanir raheem

This script uses the exact pricing from the CSV file and calculates proper weighted averages
for both transcription and optimization services.
"""

import pandas as pd
import numpy as np

class CorrectPricingCalculator:
    def __init__(self):
        """Initialize with exact pricing from CSV file."""
        print("Bismillahir rahmanir raheem")
        print("=== VoiceHype Correct Pricing Calculator ===\n")
        
        # Transcription models from CSV (already include 18% margin)
        self.transcription_models = [
            {
                'name': 'Whisper 1',
                'cost_per_minute': 0.004000000,
                'usage_weight': 0.4,  # 40%
                'provider': 'whisper'
            },
            {
                'name': 'Assembly AI Best',
                'cost_per_minute': 0.007276667,
                'usage_weight': 0.5,  # 50%
                'provider': 'assembly_ai'
            },
            {
                'name': 'Assembly AI Nano',
                'cost_per_minute': 0.002360000,
                'usage_weight': 0.1,  # 10%
                'provider': 'assembly_ai'
            }
            # Note: Assembly AI Best Real-Time (0.009200000) not included in regular transcription
        ]
        
        # LLM optimization models from Requesty (need to add 18% margin)
        self.optimization_models_raw = [
            # <PERSON> models (90% total weight)
            {
                'name': 'Claude 3.5 Sonnet',
                'input_cost_raw': 3.0 / 1_000_000,    # $3 per million
                'output_cost_raw': 15.0 / 1_000_000,  # $15 per million
                'usage_weight': 0.30
            },
            {
                'name': 'Claude 3.7 Sonnet',
                'input_cost_raw': 3.0 / 1_000_000,    # $3 per million
                'output_cost_raw': 15.0 / 1_000_000,  # $15 per million
                'usage_weight': 0.30
            },
            {
                'name': 'Claude 4 Sonnet',
                'input_cost_raw': 3.0 / 1_000_000,    # $3 per million
                'output_cost_raw': 15.0 / 1_000_000,  # $15 per million
                'usage_weight': 0.30
            },
            # Other models (10% total weight)
            {
                'name': 'Claude Haiku',
                'input_cost_raw': 0.25 / 1_000_000,   # $0.25 per million
                'output_cost_raw': 1.25 / 1_000_000,  # $1.25 per million
                'usage_weight': 0.05
            },
            {
                'name': 'Llama 3.1 70B',
                'input_cost_raw': 0.34 / 1_000_000,   # $0.34 per million
                'output_cost_raw': 0.39 / 1_000_000,  # $0.39 per million
                'usage_weight': 0.03
            },
            {
                'name': 'Llama 3.1 8B Instruct',
                'input_cost_raw': 0.05 / 1_000_000,   # $0.05 per million
                'output_cost_raw': 0.05 / 1_000_000,  # $0.05 per million
                'usage_weight': 0.01
            },
            {
                'name': 'DeepSeek v3',
                'input_cost_raw': 0.85 / 1_000_000,   # $0.85 per million
                'output_cost_raw': 0.90 / 1_000_000,  # $0.90 per million
                'usage_weight': 0.01
            }
        ]

        # Add 18% margin to optimization models
        self.optimization_models = []
        for model in self.optimization_models_raw:
            self.optimization_models.append({
                'name': model['name'],
                'input_cost': model['input_cost_raw'] * 1.18,  # Add 18% margin
                'output_cost': model['output_cost_raw'] * 1.18,  # Add 18% margin
                'usage_weight': model['usage_weight']
            })
        
        # Validate weights
        trans_weight = sum(m['usage_weight'] for m in self.transcription_models)
        opt_weight = sum(m['usage_weight'] for m in self.optimization_models)
        
        print(f"Transcription weights total: {trans_weight:.2f}")
        print(f"Optimization weights total: {opt_weight:.2f}")
        
        if abs(trans_weight - 1.0) > 0.01:
            print("⚠️  WARNING: Transcription weights don't sum to 1.0!")
        if abs(opt_weight - 1.0) > 0.01:
            print("⚠️  WARNING: Optimization weights don't sum to 1.0!")
        print()
    
    def calculate_weighted_averages(self):
        """Calculate weighted averages for all services."""
        print("=== WEIGHTED AVERAGE CALCULATIONS ===")
        print("=" * 80)
        
        # Transcription weighted average
        print("TRANSCRIPTION MODELS:")
        transcription_weighted = 0
        for model in self.transcription_models:
            contribution = model['cost_per_minute'] * model['usage_weight']
            transcription_weighted += contribution
            print(f"  {model['name']}: ${model['cost_per_minute']:.6f}/min × {model['usage_weight']*100:.0f}% = ${contribution:.6f}")
        
        print(f"\nTranscription Weighted Average: ${transcription_weighted:.6f}/min")
        print()
        
        # Optimization weighted averages
        print("OPTIMIZATION MODELS:")
        input_weighted = 0
        output_weighted = 0
        
        for model in self.optimization_models:
            input_contribution = model['input_cost'] * model['usage_weight']
            output_contribution = model['output_cost'] * model['usage_weight']
            input_weighted += input_contribution
            output_weighted += output_contribution
            
            print(f"  {model['name']} ({model['usage_weight']*100:.0f}%):")
            print(f"    Input: ${model['input_cost']*1_000_000:.2f}/M tokens → ${input_contribution*1_000_000:.2f}/M weighted")
            print(f"    Output: ${model['output_cost']*1_000_000:.2f}/M tokens → ${output_contribution*1_000_000:.2f}/M weighted")
        
        print(f"\nOptimization Weighted Averages:")
        print(f"  Input: ${input_weighted*1_000_000:.2f}/million tokens")
        print(f"  Output: ${output_weighted*1_000_000:.2f}/million tokens")
        print()
        
        return transcription_weighted, input_weighted, output_weighted
    
    def calculate_total_cost_per_minute(self, transcription_cost, input_cost_per_token, output_cost_per_token):
        """Calculate total cost per minute."""
        print("=== TOTAL COST PER MINUTE CALCULATION ===")
        print("=" * 80)
        
        # Token usage per minute
        input_tokens_per_minute = 650  # 500 prompt + 150 speech
        output_tokens_per_minute = 150  # optimized response
        
        # Calculate costs
        input_cost_per_minute = input_tokens_per_minute * input_cost_per_token
        output_cost_per_minute = output_tokens_per_minute * output_cost_per_token
        total_cost_per_minute = transcription_cost + input_cost_per_minute + output_cost_per_minute
        
        print(f"Token usage per minute:")
        print(f"  Input tokens: {input_tokens_per_minute} (500 prompt + 150 speech)")
        print(f"  Output tokens: {output_tokens_per_minute} (optimized response)")
        print()
        
        print(f"Cost breakdown per minute:")
        print(f"  Transcription: ${transcription_cost:.6f}")
        print(f"  Input LLM: {input_tokens_per_minute} × ${input_cost_per_token:.9f} = ${input_cost_per_minute:.6f}")
        print(f"  Output LLM: {output_tokens_per_minute} × ${output_cost_per_token:.9f} = ${output_cost_per_minute:.6f}")
        print(f"  TOTAL: ${total_cost_per_minute:.6f}")
        print()
        
        # Cost distribution
        trans_pct = (transcription_cost / total_cost_per_minute) * 100
        input_pct = (input_cost_per_minute / total_cost_per_minute) * 100
        output_pct = (output_cost_per_minute / total_cost_per_minute) * 100
        
        print(f"Cost distribution:")
        print(f"  Transcription: {trans_pct:.1f}%")
        print(f"  Input LLM: {input_pct:.1f}%")
        print(f"  Output LLM: {output_pct:.1f}%")
        
        return total_cost_per_minute
    
    def create_updated_csv(self, transcription_cost, input_cost, output_cost):
        """Create updated CSV with correct pricing."""
        print("\n=== CREATING UPDATED CSV ===")
        print("=" * 80)
        
        csv_data = []
        
        # Add transcription services
        csv_data.append({
            'id': 'trans_weighted',
            'service': 'transcription',
            'model': 'weighted_average',
            'cost_per_unit': transcription_cost,
            'unit': 'minute',
            'is_active': True,
            'created_at': '2025-01-23 00:00:00+00',
            'notes': 'Weighted average: 50% Assembly AI Best + 40% Whisper + 10% Assembly AI Nano'
        })
        
        # Add optimization services
        csv_data.append({
            'id': 'opt_input_weighted',
            'service': 'optimization',
            'model': 'weighted_average_input',
            'cost_per_unit': input_cost,
            'unit': 'token',
            'is_active': True,
            'created_at': '2025-01-23 00:00:00+00',
            'notes': 'Weighted average input cost: 90% Claude models + 10% others'
        })
        
        csv_data.append({
            'id': 'opt_output_weighted',
            'service': 'optimization',
            'model': 'weighted_average_output',
            'cost_per_unit': output_cost,
            'unit': 'token',
            'is_active': True,
            'created_at': '2025-01-23 00:00:00+00',
            'notes': 'Weighted average output cost: 90% Claude models + 10% others'
        })
        
        # Add total cost per minute
        total_cost = transcription_cost + (650 * input_cost) + (150 * output_cost)
        csv_data.append({
            'id': 'total_cost_per_minute',
            'service': 'total',
            'model': 'complete_service',
            'cost_per_unit': total_cost,
            'unit': 'minute',
            'is_active': True,
            'created_at': '2025-01-23 00:00:00+00',
            'notes': 'Total cost per minute: transcription + 650 input tokens + 150 output tokens'
        })
        
        df = pd.DataFrame(csv_data)
        df.to_csv('updated_service_pricing.csv', index=False)
        
        print("✅ Created: updated_service_pricing.csv")
        print("\nSummary of weighted costs:")
        print(f"  Transcription: ${transcription_cost:.6f}/minute")
        print(f"  Input tokens: ${input_cost*1_000_000:.2f}/million")
        print(f"  Output tokens: ${output_cost*1_000_000:.2f}/million")
        print(f"  Total per minute: ${total_cost:.6f}")
        
        return df

def main():
    """Main execution function."""
    calculator = CorrectPricingCalculator()
    
    # Calculate weighted averages
    transcription_cost, input_cost, output_cost = calculator.calculate_weighted_averages()
    
    # Calculate total cost per minute
    total_cost = calculator.calculate_total_cost_per_minute(transcription_cost, input_cost, output_cost)
    
    # Create updated CSV
    calculator.create_updated_csv(transcription_cost, input_cost, output_cost)
    
    print(f"\n🎉 Correct pricing calculation complete!")
    print(f"📊 Final cost per minute: ${total_cost:.6f}")

if __name__ == "__main__":
    main()
