#!/usr/bin/env python3
"""
VoiceHype Weighted Pricing Calculator
Bismillahir rahmanir raheem

This script implements weighted pricing calculations based on actual user preferences
and usage patterns, replacing the flawed simple average approach.
"""

import pandas as pd
import numpy as np

class WeightedPricingCalculator:
    def __init__(self):
        """Initialize with Requesty pricing data and usage weights."""
        print("Bismillahir rahmanir raheem")
        print("=== VoiceHype Weighted Pricing Calculator ===\n")
        
        # Requesty pricing data (per million tokens, converted to per token)
        self.models = [
            {
                'name': 'Claude 3.5 Sonnet',
                'input_cost': 3.0 / 1_000_000,    # $3 per million
                'output_cost': 15.0 / 1_000_000,  # $15 per million
                'usage_weight': 0.30,              # 30% of usage
                'provider': 'requesty'
            },
            {
                'name': 'Claude 3.7 Sonnet',
                'input_cost': 3.0 / 1_000_000,
                'output_cost': 15.0 / 1_000_000,
                'usage_weight': 0.30,              # 30% of usage
                'provider': 'requesty'
            },
            {
                'name': 'Claude 4 Sonnet',
                'input_cost': 3.0 / 1_000_000,
                'output_cost': 15.0 / 1_000_000,
                'usage_weight': 0.30,              # 30% of usage
                'provider': 'requesty'
            },
            {
                'name': 'Claude Haiku',
                'input_cost': 0.25 / 1_000_000,
                'output_cost': 1.25 / 1_000_000,
                'usage_weight': 0.05,              # 5% of usage
                'provider': 'requesty'
            },
            {
                'name': 'Llama 3.1 70B',
                'input_cost': 0.34 / 1_000_000,
                'output_cost': 0.39 / 1_000_000,
                'usage_weight': 0.03,              # 3% of usage
                'provider': 'requesty'
            },
            {
                'name': 'Llama 3.1 8B Instruct',
                'input_cost': 0.05 / 1_000_000,
                'output_cost': 0.05 / 1_000_000,
                'usage_weight': 0.01,              # 1% of usage
                'provider': 'requesty'
            },
            {
                'name': 'DeepSeek v3',
                'input_cost': 0.85 / 1_000_000,
                'output_cost': 0.90 / 1_000_000,
                'usage_weight': 0.01,              # 1% of usage
                'provider': 'requesty'
            }
        ]
        
        # Validate weights sum to 1.0
        total_weight = sum(model['usage_weight'] for model in self.models)
        print(f"Total usage weights: {total_weight:.2f}")
        if abs(total_weight - 1.0) > 0.01:
            print("⚠️  WARNING: Usage weights don't sum to 1.0!")
        print()
    
    def calculate_simple_average(self):
        """Calculate simple average (current flawed method)."""
        input_costs = [model['input_cost'] for model in self.models]
        output_costs = [model['output_cost'] for model in self.models]
        
        avg_input = np.mean(input_costs)
        avg_output = np.mean(output_costs)
        
        return avg_input, avg_output
    
    def calculate_weighted_average(self):
        """Calculate weighted average based on usage patterns."""
        weighted_input = sum(model['input_cost'] * model['usage_weight'] for model in self.models)
        weighted_output = sum(model['output_cost'] * model['usage_weight'] for model in self.models)
        
        return weighted_input, weighted_output
    
    def calculate_blended_cost_per_token(self, input_output_ratio=1.0):
        """
        Calculate blended cost per token considering input/output ratio.
        
        Args:
            input_output_ratio: Ratio of input to output tokens (default 1:1)
        """
        simple_input, simple_output = self.calculate_simple_average()
        weighted_input, weighted_output = self.calculate_weighted_average()
        
        # Calculate blended costs
        simple_blended = (simple_input + simple_output * input_output_ratio) / (1 + input_output_ratio)
        weighted_blended = (weighted_input + weighted_output * input_output_ratio) / (1 + input_output_ratio)
        
        return {
            'simple': {
                'input': simple_input,
                'output': simple_output,
                'blended': simple_blended
            },
            'weighted': {
                'input': weighted_input,
                'output': weighted_output,
                'blended': weighted_blended
            }
        }
    
    def analyze_pricing_impact(self):
        """Analyze the impact of switching from simple to weighted pricing."""
        print("=== PRICING METHODOLOGY COMPARISON ===")
        print("=" * 80)
        
        # Calculate for different input/output ratios
        ratios = [0.5, 1.0, 2.0, 3.0]  # Different scenarios
        
        for ratio in ratios:
            print(f"\nInput:Output Ratio = 1:{ratio}")
            print("-" * 50)
            
            costs = self.calculate_blended_cost_per_token(ratio)
            
            simple_cost = costs['simple']['blended']
            weighted_cost = costs['weighted']['blended']
            
            difference = weighted_cost - simple_cost
            percentage_increase = (difference / simple_cost) * 100
            
            print(f"Simple Average:   ${simple_cost:.8f} per token")
            print(f"Weighted Average: ${weighted_cost:.8f} per token")
            print(f"Difference:       ${difference:.8f} per token ({percentage_increase:+.1f}%)")
    
    def calculate_subscription_impact(self):
        """Calculate impact on subscription pricing."""
        print("\n=== SUBSCRIPTION PRICING IMPACT ===")
        print("=" * 80)
        
        # VoiceHype assumptions
        words_per_minute = 150
        tokens_per_word = 1
        fixed_prompt_tokens = 500
        transcription_cost_per_minute = 0.005709
        
        # Calculate tokens per minute
        speech_tokens_per_minute = words_per_minute * tokens_per_word
        total_tokens_per_minute = fixed_prompt_tokens + speech_tokens_per_minute
        
        print(f"Tokens per minute: {total_tokens_per_minute}")
        print(f"  - Fixed prompt: {fixed_prompt_tokens}")
        print(f"  - Speech tokens: {speech_tokens_per_minute}")
        print()
        
        # Assume 1:3 input:output ratio (typical for optimization)
        input_output_ratio = 3.0
        costs = self.calculate_blended_cost_per_token(input_output_ratio)
        
        # Calculate LLM cost per minute
        simple_llm_cost = total_tokens_per_minute * costs['simple']['blended']
        weighted_llm_cost = total_tokens_per_minute * costs['weighted']['blended']
        
        # Total cost per minute
        simple_total = transcription_cost_per_minute + simple_llm_cost
        weighted_total = transcription_cost_per_minute + weighted_llm_cost
        
        print("Cost per minute breakdown:")
        print(f"Transcription: ${transcription_cost_per_minute:.6f}")
        print(f"LLM (Simple):  ${simple_llm_cost:.6f}")
        print(f"LLM (Weighted): ${weighted_llm_cost:.6f}")
        print()
        print(f"Total (Simple):  ${simple_total:.6f}")
        print(f"Total (Weighted): ${weighted_total:.6f}")
        print(f"Increase: ${weighted_total - simple_total:.6f} ({((weighted_total - simple_total) / simple_total) * 100:+.1f}%)")
        
        return simple_total, weighted_total
    
    def analyze_tier_sustainability(self, simple_cost, weighted_cost):
        """Analyze impact on subscription tier sustainability."""
        print("\n=== TIER SUSTAINABILITY ANALYSIS ===")
        print("=" * 80)
        
        # Current tier structure
        tiers = [
            {'name': 'Basic', 'price': 9, 'margin': 0.70, 'multiplier': 1.0},
            {'name': 'Professional', 'price': 18, 'margin': 0.65, 'multiplier': 3.0}
        ]
        
        for tier in tiers:
            print(f"\n{tier['name']} Tier (${tier['price']}/month, {tier['margin']*100:.0f}% margin):")
            
            # Calculate resources with both pricing methods
            cost_budget = tier['price'] * (1 - tier['margin'])
            effective_budget = cost_budget * tier['multiplier']
            
            simple_minutes = effective_budget / simple_cost
            weighted_minutes = effective_budget / weighted_cost
            
            simple_tokens = simple_minutes * 650  # tokens per minute
            weighted_tokens = weighted_minutes * 650
            
            print(f"  Cost budget: ${cost_budget:.2f}")
            print(f"  Effective budget: ${effective_budget:.2f}")
            print(f"  Simple method:   {simple_minutes:.0f} minutes, {simple_tokens:.0f} tokens")
            print(f"  Weighted method: {weighted_minutes:.0f} minutes, {weighted_tokens:.0f} tokens")
            print(f"  Resource impact: {((weighted_minutes - simple_minutes) / simple_minutes) * 100:+.1f}% minutes")
    
    def create_updated_csv(self):
        """Create updated CSV with Requesty pricing."""
        csv_data = []
        
        for i, model in enumerate(self.models, 1):
            # Create separate entries for input and output
            csv_data.append({
                'id': f"req_{i}_input",
                'service': 'optimization',
                'model': f"{model['name'].lower().replace(' ', '-')}/input",
                'cost_per_unit': model['input_cost'],
                'unit': 'token',
                'usage_weight': model['usage_weight'],
                'provider': model['provider'],
                'is_active': True,
                'created_at': '2025-01-23 00:00:00+00'
            })
            
            csv_data.append({
                'id': f"req_{i}_output",
                'service': 'optimization',
                'model': f"{model['name'].lower().replace(' ', '-')}/output",
                'cost_per_unit': model['output_cost'],
                'unit': 'token',
                'usage_weight': model['usage_weight'],
                'provider': model['provider'],
                'is_active': True,
                'created_at': '2025-01-23 00:00:00+00'
            })
        
        df = pd.DataFrame(csv_data)
        df.to_csv('requesty_pricing_data.csv', index=False)
        print(f"\n📊 Created: requesty_pricing_data.csv")
        return df

def main():
    """Main execution function."""
    calculator = WeightedPricingCalculator()
    
    # Analyze pricing impact
    calculator.analyze_pricing_impact()
    
    # Calculate subscription impact
    simple_cost, weighted_cost = calculator.calculate_subscription_impact()
    
    # Analyze tier sustainability
    calculator.analyze_tier_sustainability(simple_cost, weighted_cost)
    
    # Create updated CSV
    calculator.create_updated_csv()
    
    print("\n🎉 Weighted pricing analysis complete!")
    print("\n💡 Key Insight: Weighted pricing likely increases costs significantly")
    print("   due to Claude model dominance (85-90% usage at premium pricing)")

if __name__ == "__main__":
    main()
