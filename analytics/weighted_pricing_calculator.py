#!/usr/bin/env python3
"""
VoiceHype Weighted Pricing Calculator
Bismillahir rahmanir raheem

This script implements weighted pricing calculations based on actual user preferences
and usage patterns, replacing the flawed simple average approach.
"""

import pandas as pd
import numpy as np

class WeightedPricingCalculator:
    def __init__(self):
        """Initialize with Requesty pricing data and usage weights."""
        print("Bismillahir rahmanir raheem")
        print("=== VoiceHype Weighted Pricing Calculator ===\n")
        
        # NOTE: Transcription costs come from original CSV data ($0.005709/min)
        # The models below are for reference only - actual cost is from CSV
        self.transcription_cost_from_csv = 0.005709

        self.transcription_models_reference = [
            {
                'name': 'Assembly AI Best',
                'cost_per_minute': 0.006,
                'usage_weight': 0.5,
                'provider': 'assembly_ai'
            },
            {
                'name': 'Assembly AI Nano',
                'cost_per_minute': 0.004,
                'usage_weight': 0.1,
                'provider': 'assembly_ai'
            },
            {
                'name': 'Whisper (<PERSON> Fox)',
                'cost_per_minute': 0.007,
                'usage_weight': 0.4,
                'provider': 'lemon_fox'
            }
        ]

        # LLM optimization models (per million tokens, converted to per token)
        self.optimization_models = [
            {
                'name': 'Claude 3.5 Sonnet',
                'input_cost': 3.0 / 1_000_000,    # $3 per million
                'output_cost': 15.0 / 1_000_000,  # $15 per million
                'usage_weight': 0.30,              # 30% of usage
                'provider': 'requesty'
            },
            {
                'name': 'Claude 3.7 Sonnet',
                'input_cost': 3.0 / 1_000_000,
                'output_cost': 15.0 / 1_000_000,
                'usage_weight': 0.30,              # 30% of usage
                'provider': 'requesty'
            },
            {
                'name': 'Claude 4 Sonnet',
                'input_cost': 3.0 / 1_000_000,
                'output_cost': 15.0 / 1_000_000,
                'usage_weight': 0.30,              # 30% of usage
                'provider': 'requesty'
            },
            {
                'name': 'Claude Haiku',
                'input_cost': 0.25 / 1_000_000,
                'output_cost': 1.25 / 1_000_000,
                'usage_weight': 0.05,              # 5% of usage
                'provider': 'requesty'
            },
            {
                'name': 'Llama 3.1 70B',
                'input_cost': 0.34 / 1_000_000,
                'output_cost': 0.39 / 1_000_000,
                'usage_weight': 0.03,              # 3% of usage
                'provider': 'requesty'
            },
            {
                'name': 'Llama 3.1 8B Instruct',
                'input_cost': 0.05 / 1_000_000,
                'output_cost': 0.05 / 1_000_000,
                'usage_weight': 0.01,              # 1% of usage
                'provider': 'requesty'
            },
            {
                'name': 'DeepSeek v3',
                'input_cost': 0.85 / 1_000_000,
                'output_cost': 0.90 / 1_000_000,
                'usage_weight': 0.01,              # 1% of usage
                'provider': 'requesty'
            }
        ]

        # Validate weights sum to 1.0 for both model types
        transcription_weight = sum(model['usage_weight'] for model in self.transcription_models_reference)
        optimization_weight = sum(model['usage_weight'] for model in self.optimization_models)

        print(f"Transcription model weights: {transcription_weight:.2f}")
        print(f"Optimization model weights: {optimization_weight:.2f}")

        if abs(transcription_weight - 1.0) > 0.01:
            print("⚠️  WARNING: Transcription weights don't sum to 1.0!")
        if abs(optimization_weight - 1.0) > 0.01:
            print("⚠️  WARNING: Optimization weights don't sum to 1.0!")
        print()
    
    def calculate_transcription_costs(self):
        """Calculate transcription costs (simple and weighted)."""
        # Simple average
        simple_transcription = np.mean([model['cost_per_minute'] for model in self.transcription_models_reference])

        # Weighted average
        weighted_transcription = sum(model['cost_per_minute'] * model['usage_weight']
                                   for model in self.transcription_models_reference)

        return simple_transcription, weighted_transcription

    def calculate_optimization_costs(self):
        """Calculate optimization costs (simple and weighted)."""
        # Simple averages
        input_costs = [model['input_cost'] for model in self.optimization_models]
        output_costs = [model['output_cost'] for model in self.optimization_models]

        simple_input = np.mean(input_costs)
        simple_output = np.mean(output_costs)

        # Weighted averages
        weighted_input = sum(model['input_cost'] * model['usage_weight']
                           for model in self.optimization_models)
        weighted_output = sum(model['output_cost'] * model['usage_weight']
                            for model in self.optimization_models)

        return simple_input, simple_output, weighted_input, weighted_output
    
    def calculate_blended_cost_per_token(self, input_output_ratio=1.0):
        """
        Calculate blended cost per token considering input/output ratio.
        
        Args:
            input_output_ratio: Ratio of input to output tokens (default 1:1)
        """
        simple_input, simple_output = self.calculate_simple_average()
        weighted_input, weighted_output = self.calculate_weighted_average()
        
        # Calculate blended costs
        simple_blended = (simple_input + simple_output * input_output_ratio) / (1 + input_output_ratio)
        weighted_blended = (weighted_input + weighted_output * input_output_ratio) / (1 + input_output_ratio)
        
        return {
            'simple': {
                'input': simple_input,
                'output': simple_output,
                'blended': simple_blended
            },
            'weighted': {
                'input': weighted_input,
                'output': weighted_output,
                'blended': weighted_blended
            }
        }
    
    def analyze_pricing_impact(self):
        """Analyze the impact of switching from simple to weighted pricing."""
        print("=== PRICING METHODOLOGY COMPARISON ===")
        print("=" * 80)
        
        # Calculate for different input/output ratios
        ratios = [0.5, 1.0, 2.0, 3.0]  # Different scenarios
        
        for ratio in ratios:
            print(f"\nInput:Output Ratio = 1:{ratio}")
            print("-" * 50)
            
            costs = self.calculate_blended_cost_per_token(ratio)
            
            simple_cost = costs['simple']['blended']
            weighted_cost = costs['weighted']['blended']
            
            difference = weighted_cost - simple_cost
            percentage_increase = (difference / simple_cost) * 100
            
            print(f"Simple Average:   ${simple_cost:.8f} per token")
            print(f"Weighted Average: ${weighted_cost:.8f} per token")
            print(f"Difference:       ${difference:.8f} per token ({percentage_increase:+.1f}%)")
    
    def calculate_subscription_impact(self):
        """Calculate impact on subscription pricing."""
        print("\n=== SUBSCRIPTION PRICING IMPACT ===")
        print("=" * 80)
        
        # VoiceHype assumptions
        words_per_minute = 150
        tokens_per_word = 1
        fixed_prompt_tokens = 500
        transcription_cost_per_minute = 0.005709
        
        # Calculate tokens per minute
        speech_tokens_per_minute = words_per_minute * tokens_per_word
        total_tokens_per_minute = fixed_prompt_tokens + speech_tokens_per_minute
        
        print(f"Tokens per minute: {total_tokens_per_minute}")
        print(f"  - Fixed prompt: {fixed_prompt_tokens}")
        print(f"  - Speech tokens: {speech_tokens_per_minute}")
        print()
        
        # Assume 1:3 input:output ratio (typical for optimization)
        input_output_ratio = 3.0
        costs = self.calculate_blended_cost_per_token(input_output_ratio)
        
        # Calculate LLM cost per minute
        simple_llm_cost = total_tokens_per_minute * costs['simple']['blended']
        weighted_llm_cost = total_tokens_per_minute * costs['weighted']['blended']
        
        # Total cost per minute
        simple_total = transcription_cost_per_minute + simple_llm_cost
        weighted_total = transcription_cost_per_minute + weighted_llm_cost
        
        print("Cost per minute breakdown:")
        print(f"Transcription: ${transcription_cost_per_minute:.6f}")
        print(f"LLM (Simple):  ${simple_llm_cost:.6f}")
        print(f"LLM (Weighted): ${weighted_llm_cost:.6f}")
        print()
        print(f"Total (Simple):  ${simple_total:.6f}")
        print(f"Total (Weighted): ${weighted_total:.6f}")
        print(f"Increase: ${weighted_total - simple_total:.6f} ({((weighted_total - simple_total) / simple_total) * 100:+.1f}%)")
        
        return simple_total, weighted_total
    
    def analyze_tier_sustainability(self, simple_cost, weighted_cost):
        """Analyze impact on subscription tier sustainability."""
        print("\n=== TIER SUSTAINABILITY ANALYSIS ===")
        print("=" * 80)
        
        # Current tier structure
        tiers = [
            {'name': 'Basic', 'price': 9, 'margin': 0.70, 'multiplier': 1.0},
            {'name': 'Professional', 'price': 18, 'margin': 0.65, 'multiplier': 3.0}
        ]
        
        for tier in tiers:
            print(f"\n{tier['name']} Tier (${tier['price']}/month, {tier['margin']*100:.0f}% margin):")
            
            # Calculate resources with both pricing methods
            cost_budget = tier['price'] * (1 - tier['margin'])
            effective_budget = cost_budget * tier['multiplier']
            
            simple_minutes = effective_budget / simple_cost
            weighted_minutes = effective_budget / weighted_cost
            
            simple_tokens = simple_minutes * 520  # tokens per minute (370 input + 150 output)
            weighted_tokens = weighted_minutes * 520
            
            print(f"  Cost budget: ${cost_budget:.2f}")
            print(f"  Effective budget: ${effective_budget:.2f}")
            print(f"  Simple method:   {simple_minutes:.0f} minutes, {simple_tokens:.0f} tokens")
            print(f"  Weighted method: {weighted_minutes:.0f} minutes, {weighted_tokens:.0f} tokens")
            print(f"  Resource impact: {((weighted_minutes - simple_minutes) / simple_minutes) * 100:+.1f}% minutes")
    
    def create_updated_csv(self):
        """Create updated CSV with Requesty pricing."""
        csv_data = []
        
        for i, model in enumerate(self.models, 1):
            # Create separate entries for input and output
            csv_data.append({
                'id': f"req_{i}_input",
                'service': 'optimization',
                'model': f"{model['name'].lower().replace(' ', '-')}/input",
                'cost_per_unit': model['input_cost'],
                'unit': 'token',
                'usage_weight': model['usage_weight'],
                'provider': model['provider'],
                'is_active': True,
                'created_at': '2025-01-23 00:00:00+00'
            })
            
            csv_data.append({
                'id': f"req_{i}_output",
                'service': 'optimization',
                'model': f"{model['name'].lower().replace(' ', '-')}/output",
                'cost_per_unit': model['output_cost'],
                'unit': 'token',
                'usage_weight': model['usage_weight'],
                'provider': model['provider'],
                'is_active': True,
                'created_at': '2025-01-23 00:00:00+00'
            })
        
        df = pd.DataFrame(csv_data)
        df.to_csv('requesty_pricing_data.csv', index=False)
        print(f"\n📊 Created: requesty_pricing_data.csv")
        return df

    def verify_weighted_calculations(self):
        """Verify that weighted calculations include ALL models properly."""
        print("=== WEIGHTED CALCULATION VERIFICATION ===")
        print("=" * 80)

        # Transcription verification
        print("TRANSCRIPTION MODELS:")
        simple_trans, weighted_trans = self.calculate_transcription_costs()

        for model in self.transcription_models_reference:
            print(f"  {model['name']}: ${model['cost_per_minute']:.6f}/min (weight: {model['usage_weight']*100:.0f}%)")

        print(f"\nSimple Average: ${simple_trans:.6f}/min")
        print(f"Weighted Average: ${weighted_trans:.6f}/min")
        print()

        # Optimization verification
        print("OPTIMIZATION MODELS:")
        simple_input, simple_output, weighted_input, weighted_output = self.calculate_optimization_costs()

        for model in self.optimization_models:
            print(f"  {model['name']}:")
            print(f"    Input: ${model['input_cost']*1_000_000:.2f}/million tokens (weight: {model['usage_weight']*100:.0f}%)")
            print(f"    Output: ${model['output_cost']*1_000_000:.2f}/million tokens")

        print(f"\nSimple Averages:")
        print(f"  Input: ${simple_input*1_000_000:.2f}/million tokens")
        print(f"  Output: ${simple_output*1_000_000:.2f}/million tokens")

        print(f"\nWeighted Averages:")
        print(f"  Input: ${weighted_input*1_000_000:.2f}/million tokens")
        print(f"  Output: ${weighted_output*1_000_000:.2f}/million tokens")
        print()

        # Final cost calculation - UPDATED with optimized PromptFormatter
        input_tokens_per_minute = 370  # 220 prompt + 150 speech (reduced from 650!)
        output_tokens_per_minute = 150  # response

        input_cost_per_minute = input_tokens_per_minute * weighted_input
        output_cost_per_minute = output_tokens_per_minute * weighted_output
        total_llm_cost = input_cost_per_minute + output_cost_per_minute
        total_cost_per_minute = weighted_trans + total_llm_cost

        print("FINAL COST CALCULATION:")
        print(f"Transcription: ${weighted_trans:.6f}/min")
        print(f"Input LLM ({input_tokens_per_minute} tokens): ${input_cost_per_minute:.6f}/min")
        print(f"Output LLM ({output_tokens_per_minute} tokens): ${output_cost_per_minute:.6f}/min")
        print(f"TOTAL: ${total_cost_per_minute:.6f}/min")

        return total_cost_per_minute

def main():
    """Main execution function."""
    calculator = WeightedPricingCalculator()

    # Verify weighted calculations
    total_cost = calculator.verify_weighted_calculations()

    print("\n✅ VERIFICATION COMPLETE!")
    print(f"📊 All models properly included in weighted calculation")
    print(f"💰 Final cost per minute: ${total_cost:.6f}")

if __name__ == "__main__":
    main()
