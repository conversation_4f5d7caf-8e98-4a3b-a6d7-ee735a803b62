#!/usr/bin/env python3
"""
VoiceHype Weighted Pricing Profit Margin Tables
Bismillahir rahmanir raheem

This script creates comprehensive profit margin tables using the new weighted pricing methodology
to show accurate resource allocations at different price points and profit margins.
"""

import pandas as pd
import numpy as np

class WeightedPricingProfitTables:
    def __init__(self):
        """Initialize with weighted pricing cost structure."""
        print("Bismillahir rahmanir raheem")
        print("=== VoiceHype Weighted Pricing Profit Margin Tables ===\n")
        
        # FINAL CORRECT PRICING from proper Requesty data
        # Transcription weighted average: 60% Assembly AI Real-Time + 25% Assembly AI Best + 10% Whisper + 5% Assembly AI Nano
        self.transcription_cost_per_minute = 0.007857

        # Usage assumptions - UPDATED with optimized PromptFormatter
        self.words_per_minute = 150
        self.tokens_per_word = 1
        self.fixed_prompt_tokens = 220  # REDUCED from 500 to 220 (63% reduction!)

        # Calculate token breakdown per minute
        self.speech_tokens_per_minute = self.words_per_minute * self.tokens_per_word  # Speech input: 150
        self.input_tokens_per_minute = self.fixed_prompt_tokens + self.speech_tokens_per_minute  # Total input: 370
        self.output_tokens_per_minute = self.words_per_minute * self.tokens_per_word  # Optimized response: 150

        # FINAL weighted average costs from Requesty pricing (includes 18% margin)
        self.weighted_input_cost_per_token = 0.000003223   # $3.22/million tokens
        self.weighted_output_cost_per_token = 0.000016029  # $16.03/million tokens

        # Calculate LLM costs per minute
        self.input_cost_per_minute = self.input_tokens_per_minute * self.weighted_input_cost_per_token
        self.output_cost_per_minute = self.output_tokens_per_minute * self.weighted_output_cost_per_token
        self.total_llm_cost_per_minute = self.input_cost_per_minute + self.output_cost_per_minute

        # Total cost per minute
        self.total_weighted_cost_per_minute = self.transcription_cost_per_minute + self.total_llm_cost_per_minute
        
        print(f"FINAL CORRECT WEIGHTED PRICING COST STRUCTURE:")
        print(f"Transcription cost per minute: ${self.transcription_cost_per_minute:.6f}")
        print(f"Weighted input cost per token: ${self.weighted_input_cost_per_token:.9f} (${self.weighted_input_cost_per_token*1_000_000:.2f}/million)")
        print(f"Weighted output cost per token: ${self.weighted_output_cost_per_token:.9f} (${self.weighted_output_cost_per_token*1_000_000:.2f}/million)")
        print(f"Input cost per minute: ${self.input_cost_per_minute:.6f}")
        print(f"Output cost per minute: ${self.output_cost_per_minute:.6f}")
        print(f"Total LLM cost per minute: ${self.total_llm_cost_per_minute:.6f}")
        print(f"TOTAL COST PER MINUTE: ${self.total_weighted_cost_per_minute:.6f}")
        print(f"Token breakdown per minute:")
        print(f"  - Input tokens: {self.input_tokens_per_minute} (500 prompt + 150 speech)")
        print(f"  - Output tokens: {self.output_tokens_per_minute} (optimized response)")
        print(f"  - TOTAL: {self.input_tokens_per_minute + self.output_tokens_per_minute}")
        print(f"Cost distribution:")
        print(f"  - Transcription: {(self.transcription_cost_per_minute/self.total_weighted_cost_per_minute)*100:.1f}%")
        print(f"  - Input LLM: {(self.input_cost_per_minute/self.total_weighted_cost_per_minute)*100:.1f}%")
        print(f"  - Output LLM: {(self.output_cost_per_minute/self.total_weighted_cost_per_minute)*100:.1f}%")
        print()
    
    def calculate_resources_by_margin(self, price, profit_margins, usage_multiplier=1.0):
        """Calculate minutes and tokens for different profit margins using weighted pricing."""
        results = []
        
        for margin in profit_margins:
            # Calculate cost budget available after profit margin
            cost_budget = price * (1 - margin)
            
            # Apply usage multiplier for competitive advantage
            effective_budget = cost_budget * usage_multiplier
            
            # Calculate minutes allocation using weighted cost
            minutes = effective_budget / self.total_weighted_cost_per_minute
            
            # Calculate tokens allocation
            tokens = minutes * (self.input_tokens_per_minute + self.output_tokens_per_minute)
            
            results.append({
                'profit_margin': margin,
                'profit_margin_pct': f"{margin*100:.0f}%",
                'cost_budget': cost_budget,
                'effective_budget': effective_budget,
                'minutes': int(minutes),
                'tokens': int(tokens),
                'minutes_formatted': f"{int(minutes):,}",
                'tokens_formatted': f"{int(tokens):,}"
            })
        
        return results
    
    def create_comprehensive_profit_tables(self):
        """Create comprehensive profit margin tables for multiple price points."""
        print("=== COMPREHENSIVE WEIGHTED PRICING PROFIT MARGIN TABLES ===")
        print("=" * 100)
        
        # Define profit margins from 0% to 95% (100% would give zero resources)
        profit_margins = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.65, 0.7, 0.75, 0.8, 0.85, 0.9, 0.95]
        
        # Define price points to analyze
        price_points = [9, 11, 12, 15, 18, 22, 25, 30]
        
        all_results = {}
        
        for price in price_points:
            print(f"\n=== ${price}/month Pricing Analysis ===")
            print("=" * 80)
            
            results = self.calculate_resources_by_margin(price, profit_margins, usage_multiplier=1.0)
            all_results[price] = results
            
            # Print formatted table
            print(f"{'Profit Margin':<15} {'Cost Budget':<12} {'Minutes':<12} {'Tokens':<15}")
            print("-" * 80)
            
            for result in results:
                marker = ""
                if result['profit_margin'] == 0.65:
                    marker = " ← RECOMMENDED"
                elif result['profit_margin'] == 0.7:
                    marker = " ← CURRENT BASIC"
                
                print(f"{result['profit_margin_pct']:<15} "
                      f"${result['cost_budget']:<11.2f} "
                      f"{result['minutes_formatted']:<12} "
                      f"{result['tokens_formatted']:<15}{marker}")
            
            # Highlight key scenarios
            zero_margin = results[0]  # 0% margin
            recommended_margin = results[7]  # 65% margin
            
            print(f"\n🌟 MARKETING HIGHLIGHT (0% margin): {zero_margin['minutes_formatted']} minutes + {zero_margin['tokens_formatted']} tokens")
            print(f"💼 RECOMMENDED ALLOCATION (65% margin): {recommended_margin['minutes_formatted']} minutes + {recommended_margin['tokens_formatted']} tokens")
        
        return all_results
    
    def create_comparison_table(self, all_results):
        """Create side-by-side comparison table for all price points."""
        print(f"\n=== SIDE-BY-SIDE COMPARISON: WEIGHTED PRICING ===")
        print("=" * 150)
        
        # Create header
        price_points = sorted(all_results.keys())
        header = f"{'Margin':<8} "
        for price in price_points:
            header += f"| ${price} Minutes ${price} Tokens    "
        print(header)
        print("-" * 150)
        
        # Print data rows for key margins
        key_margins = [0.0, 0.2, 0.4, 0.6, 0.65, 0.7, 0.8, 0.9]
        
        for margin in key_margins:
            margin_pct = f"{margin*100:.0f}%"
            row = f"{margin_pct:<8} "
            
            for price in price_points:
                # Find the result for this margin
                result = next(r for r in all_results[price] if r['profit_margin'] == margin)
                row += f"| {result['minutes_formatted']:<8} {result['tokens_formatted']:<12} "
            
            print(row)
    
    def analyze_hybrid_pricing_scenarios(self):
        """Analyze specific hybrid pricing scenarios."""
        print(f"\n=== HYBRID PRICING SCENARIOS ANALYSIS ===")
        print("=" * 80)
        
        scenarios = [
            {'name': 'Current Basic', 'price': 9, 'margin': 0.7, 'multiplier': 1.0},
            {'name': 'Hybrid Basic Option 1', 'price': 11, 'margin': 0.65, 'multiplier': 1.0},
            {'name': 'Hybrid Basic Option 2', 'price': 12, 'margin': 0.6, 'multiplier': 1.0},
            {'name': 'Current Professional', 'price': 18, 'margin': 0.65, 'multiplier': 3.0},
            {'name': 'Hybrid Professional Option 1', 'price': 22, 'margin': 0.6, 'multiplier': 3.0},
            {'name': 'Hybrid Professional Option 2', 'price': 25, 'margin': 0.55, 'multiplier': 3.0},
        ]
        
        print(f"{'Scenario':<30} {'Price':<8} {'Margin':<8} {'Multiplier':<10} {'Minutes':<10} {'Tokens':<12}")
        print("-" * 80)
        
        for scenario in scenarios:
            results = self.calculate_resources_by_margin(
                scenario['price'], 
                [scenario['margin']], 
                scenario['multiplier']
            )
            result = results[0]
            
            print(f"{scenario['name']:<30} "
                  f"${scenario['price']:<7} "
                  f"{scenario['margin']*100:.0f}%{'':<5} "
                  f"{scenario['multiplier']:.1f}x{'':<7} "
                  f"{result['minutes_formatted']:<10} "
                  f"{result['tokens_formatted']:<12}")
    
    def create_marketing_impact_analysis(self, all_results):
        """Create marketing impact analysis showing psychological numbers."""
        print(f"\n=== MARKETING PSYCHOLOGICAL IMPACT ANALYSIS ===")
        print("=" * 80)
        
        key_prices = [9, 11, 15, 18, 22, 25]
        
        for price in key_prices:
            if price in all_results:
                zero_margin = all_results[price][0]  # 0% margin
                low_margin = all_results[price][2]   # 20% margin
                
                print(f"\n${price}/month Marketing Numbers:")
                print(f"  💫 Maximum Potential (0% margin):")
                print(f"     {zero_margin['minutes_formatted']} minutes + {zero_margin['tokens_formatted']} tokens")
                print(f"     ({zero_margin['minutes']/1000:.1f}K minutes, {zero_margin['tokens']/1000:.0f}K tokens)")
                
                print(f"  ⭐ Competitive Showcase (20% margin):")
                print(f"     {low_margin['minutes_formatted']} minutes + {low_margin['tokens_formatted']} tokens")
                print(f"     ({low_margin['minutes']/1000:.1f}K minutes, {low_margin['tokens']/1000:.0f}K tokens)")
    
    def save_comprehensive_analysis(self, all_results):
        """Save all results to CSV files."""
        # Save individual price point analyses
        for price, results in all_results.items():
            df = pd.DataFrame(results)
            filename = f"weighted_pricing_{price}_dollar_analysis.csv"
            df.to_csv(filename, index=False)
            print(f"Saved: {filename}")
        
        # Create combined comparison CSV
        combined_data = []
        profit_margins = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.65, 0.7, 0.75, 0.8, 0.85, 0.9, 0.95]
        
        for i, margin in enumerate(profit_margins):
            row = {'profit_margin': f"{margin*100:.0f}%"}
            
            for price in sorted(all_results.keys()):
                result = all_results[price][i]
                row[f'price_{price}_minutes'] = result['minutes']
                row[f'price_{price}_tokens'] = result['tokens']
            
            combined_data.append(row)
        
        combined_df = pd.DataFrame(combined_data)
        combined_df.to_csv('weighted_pricing_all_scenarios.csv', index=False)
        print("Saved: weighted_pricing_all_scenarios.csv")

def main():
    """Main execution function."""
    analyzer = WeightedPricingProfitTables()
    
    # Create comprehensive profit tables
    all_results = analyzer.create_comprehensive_profit_tables()
    
    # Create comparison table
    analyzer.create_comparison_table(all_results)
    
    # Analyze hybrid pricing scenarios
    analyzer.analyze_hybrid_pricing_scenarios()
    
    # Create marketing impact analysis
    analyzer.create_marketing_impact_analysis(all_results)
    
    # Save comprehensive analysis
    analyzer.save_comprehensive_analysis(all_results)
    
    print(f"\n🎉 Weighted pricing profit margin analysis complete!")
    print(f"📊 Files created:")
    print(f"   - Individual price point CSV files")
    print(f"   - weighted_pricing_all_scenarios.csv")
    print(f"\n💡 Key Insight: Weighted pricing provides accurate resource allocation")
    print(f"   based on real user preferences (90% Claude usage)")

if __name__ == "__main__":
    main()
